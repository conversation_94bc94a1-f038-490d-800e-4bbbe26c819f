FROM python:3.9-slim

# ---- System dependencies & Google Cloud SDK ----
RUN apt-get update \
&& apt-get install -y --no-install-recommends curl gnupg \
# add the Cloud SDK distribution URI as a package source
&& curl -sSL https://packages.cloud.google.com/apt/doc/apt-key.gpg | \
   gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg \
&& echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" \
   > /etc/apt/sources.list.d/google-cloud-sdk.list \
# install the Cloud SDK
&& apt-get update && apt-get install -y google-cloud-sdk \
# clean up
&& apt-get clean && rm -rf /var/lib/apt/lists/*

RUN apt-get update && \
    apt-get install -y openjdk-17-jdk-headless && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV PATH="$JAVA_HOME/bin:$PATH"

# Python パッケージ
COPY requirements.txt ${pwd}

# pipのアップデート
RUN pip install --upgrade pip

# pythonパッケージをインストール
RUN pip install -r requirements.txt

CMD ["/bin/bash"]
