services:                           # サービスを定義しますという決まり文句
  python:                           # pythonという名前でサービスを定義
    image: purchasing_prediction_model:v1             # イメージのREPOSITORY名:TAG名を指定
    build: .                        # Dockerfileを相対パスで指定
    container_name: two-tower-model    # 作成されるコンテナ名を指定
    working_dir: /workspace     # 作業ディレクトリを指定
    volumes:                        # マウントするファイルを指定する
      - ..:/src      # ローカルPCのsrc:コンテナのworkspace/src
    tty: true                       # コンテナを起動し続ける