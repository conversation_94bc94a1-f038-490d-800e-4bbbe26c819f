{
    "name": "Two Tower Model",                    // VScodeがコンテナに接続したときのタイトルを自由に設定
    "dockerComposeFile": "docker-compose.yml",  // docker-conpose.ymlファイルを指定
    "service": "python",                        // docker-conpose.ymlファイル内に記載したサービス名を指定
    "workspaceFolder": "/src",         // VScode接続時に開くフォルダを指定
    "customizations": {
        "vscode": {
            "extensions": [                     // コンテナ作成時にインストールする拡張機能を記載　
                "ms-python.python"            // python拡張機能
            ],
            "settings": {                       // VScodeの設定を記載
                "files.autoSave": "afterDelay"  // ファイルのオートセーブ
            }
        }
    }
}
