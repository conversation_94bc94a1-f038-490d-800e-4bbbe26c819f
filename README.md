# 特徴量の追加・絞り込み
1. `user_item_table.sql`を変更
    - 作成するテーブル名の変更（例：v1 -> v2）
    - 特徴量の追加・絞り込み
2. `train_split.sql`、`test_split.sql`、`valid_split.sql`
    - 作成するテーブル名（例：v1 -> v2）
    - 参照するテーブル名（`user_item_table.sql`で作成したテーブル名）
3. `model_config.yaml`を変更
    - cat_features
    - num_features
    - model_config
        - item_num_cols
        - item_cat_cols
        - user_num_cols
        - user_cat_cols
4. `preprocess.sql`を変更
    - 作成するテーブル名（例：v1 -> v2）
    - 参照するテーブル名（`train_split.sql`で作成したテーブル名）
5. `TRAIN_TABLE_ID`という定数を`train_split.sql`で作成したテーブル名に変更
6. `bq_to_gcs_train.sql`を変更
    - 保存するフォルダの名前を変更
    - `preprocess.sql`で作成したテーブル名に変更
7. `GCS_FOLDER_FOR_TRAIN_DATA`という定数を`bq_to_gcs_train.sql`で作成したフォルダ名に変更




