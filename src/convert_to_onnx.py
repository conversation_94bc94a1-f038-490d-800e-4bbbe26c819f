import torch
import torch.onnx
import yaml
import json
from train_model import UserEmbeddingModel, ItemEmbeddingModel
import numpy as np

def convert_user_model_to_onnx(model_path, config_path, output_path):
    """
    Convert the user embedding model to ONNX format.
    The model expects preprocessed features (normalized numerical, encoded categorical).
    """
    # Load config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Load the trained model
    device = torch.device('cpu')  # Use CPU for ONNX export
    model = UserEmbeddingModel(config).to(device)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval()
    
    # Create dummy inputs matching the expected input format
    batch_size = 1
    num_features_count = len(config['model_config']['user_num_cols'])
    cat_features_count = len(config['model_config']['user_cat_cols'])
    
    # Dummy inputs (already preprocessed)
    dummy_num_features = torch.randn(batch_size, num_features_count, dtype=torch.float32)
    dummy_cat_features = torch.randint(0, 10, (batch_size, cat_features_count), dtype=torch.long)
    
    # Export to ONNX
    torch.onnx.export(
        model,
        (dummy_num_features, dummy_cat_features),
        output_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['numerical_features', 'categorical_features'],
        output_names=['user_embedding'],
        dynamic_axes={
            'numerical_features': {0: 'batch_size'},
            'categorical_features': {0: 'batch_size'},
            'user_embedding': {0: 'batch_size'}
        }
    )
    print(f"User model exported to {output_path}")

def convert_item_model_to_onnx(model_path, config_path, output_path):
    """
    Convert the item embedding model to ONNX format.
    """
    # Load config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Load the trained model
    device = torch.device('cpu')
    model = ItemEmbeddingModel(config).to(device)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval()
    
    # Create dummy inputs
    batch_size = 1
    num_features_count = len(config['model_config']['item_num_cols'])
    cat_features_count = len(config['model_config']['item_cat_cols'])
    
    dummy_num_features = torch.randn(batch_size, num_features_count, dtype=torch.float32)
    dummy_cat_features = torch.randint(0, 10, (batch_size, cat_features_count), dtype=torch.long)
    
    # Export to ONNX
    torch.onnx.export(
        model,
        (dummy_num_features, dummy_cat_features),
        output_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['numerical_features', 'categorical_features'],
        output_names=['item_embedding'],
        dynamic_axes={
            'numerical_features': {0: 'batch_size'},
            'categorical_features': {0: 'batch_size'},
            'item_embedding': {0: 'batch_size'}
        }
    )
    print(f"Item model exported to {output_path}")

def create_bigquery_preprocessing_sql():
    """
    Generate SQL for preprocessing raw features in BigQuery.
    This SQL should match the preprocessing done during training.
    """
    sql_template = """
    -- Preprocessing SQL for BigQuery ML PREDICT
    WITH preprocessed_features AS (
      SELECT 
        adid,
        -- Normalize numerical features (you'll need to replace with actual mean/std values)
        (probability_age_10s - {prob_age_10s_mean}) / {prob_age_10s_std} AS probability_age_10s_norm,
        (probability_age_20s - {prob_age_20s_mean}) / {prob_age_20s_std} AS probability_age_20s_norm,
        -- ... add all other numerical features with their normalization parameters
        
        -- Encode categorical features (you'll need the actual encoding mappings)
        CASE 
          WHEN home_pref = 'Tokyo' THEN 1
          WHEN home_pref = 'Osaka' THEN 2
          -- ... add all categorical mappings
          ELSE 0
        END AS home_pref_encoded,
        
        -- Create arrays for model input
        [probability_age_10s_norm, probability_age_20s_norm, ...] AS numerical_features,
        [home_pref_encoded, home_city_encoded, ...] AS categorical_features
        
      FROM your_raw_data_table
    )
    
    SELECT 
      adid,
      ML.PREDICT(
        MODEL `your_project.your_dataset.user_embedding_onnx_model`,
        (SELECT AS STRUCT numerical_features, categorical_features)
      ) AS user_embedding
    FROM preprocessed_features
    """
    return sql_template

class UserModelWithPreprocessing(torch.nn.Module):
    """
    User model that includes preprocessing logic.
    This version takes raw features and applies normalization/encoding internally.
    """
    def __init__(self, base_model, num_means, num_stds, cat_encodings, config):
        super().__init__()
        self.base_model = base_model
        self.register_buffer('num_means', torch.tensor(num_means, dtype=torch.float32))
        self.register_buffer('num_stds', torch.tensor(num_stds, dtype=torch.float32))

        # Store categorical encodings as parameters
        self.cat_encodings = cat_encodings
        self.user_num_cols = config['model_config']['user_num_cols']
        self.user_cat_cols = config['model_config']['user_cat_cols']

    def forward(self, raw_num_features, raw_cat_features_str):
        # Normalize numerical features
        normalized_num = (raw_num_features - self.num_means) / self.num_stds

        # Note: String encoding in ONNX is complex, so this approach has limitations
        # You might need to pass pre-encoded categorical features

        return self.base_model(normalized_num, raw_cat_features_str)

def convert_user_model_with_preprocessing_to_onnx(model_path, config_path, num_scaler_path, cat_encoding_path, output_path):
    """
    Convert user model with preprocessing included.
    Note: This approach has limitations with string categorical features in ONNX.
    """
    # Load artifacts
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    with open(num_scaler_path, 'r') as f:
        num_scaler = json.load(f)

    with open(cat_encoding_path, 'r') as f:
        cat_encodings = json.load(f)

    # Load base model
    device = torch.device('cpu')
    base_model = UserEmbeddingModel(config).to(device)
    base_model.load_state_dict(torch.load(model_path, map_location=device))
    base_model.eval()

    # Create model with preprocessing
    model_with_preprocessing = UserModelWithPreprocessing(
        base_model,
        num_scaler['means'],
        num_scaler['stds'],
        cat_encodings,
        config
    )

    # This approach is complex due to string handling in ONNX
    print("Warning: Including string categorical preprocessing in ONNX is complex.")
    print("Consider using Option 1 (BigQuery preprocessing) instead.")

if __name__ == "__main__":
    # Example usage
    config_path = "config/model_config.yaml"

    # Convert models (you'll need to download the .pt files from GCS first)
    convert_user_model_to_onnx(
        model_path="user_embedding_model.pt",
        config_path=config_path,
        output_path="user_embedding_model.onnx"
    )

    convert_item_model_to_onnx(
        model_path="item_embedding_model.pt",
        config_path=config_path,
        output_path="item_embedding_model.onnx"
    )

    print("ONNX conversion completed!")
    print("\nNext steps:")
    print("1. Upload ONNX models to BigQuery ML")
    print("2. Create preprocessing SQL with actual normalization parameters")
    print("3. Use ML.PREDICT with preprocessed features")
