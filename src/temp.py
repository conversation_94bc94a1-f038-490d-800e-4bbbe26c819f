import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions

if __name__ == '__main__':
    project = 'labs-science'
    dataset = 'works_ueno'
    table_spec = f'{project}:{dataset}.two_tower_model_sugi_drink_data'  # 実在のテーブル名

    options_dict = {
        'project': project,
        'runner': 'DirectRunner',
        'temp_location': f'gs://{dataset}/tmp',
    }
    pipeline_options = PipelineOptions().from_dictionary(options_dict)

    with beam.Pipeline(options=pipeline_options) as p:
        (
            p
            | 'ReadFromBQ'   >> beam.io.ReadFromBigQuery(table=table_spec)
            | 'WriteToLocal' >> beam.io.WriteToText('/tmp/bq_only_test', file_name_suffix='.json')
        )