from kfp.dsl import (component, Dataset, Model, Input, Output)

@component(
    packages_to_install=[
        "pyyaml",
        "google-cloud-bigquery",
        "gcsfs", 
    ],
    base_image="python:3.9",
)
def compute_normalization_parameters(
    project_id: str,
    config: dict,
    train_table_id: str,
    output_num_scaler: Output[Dataset],
):
    import yaml
    from google.cloud import bigquery
    import json
    import fsspec

    num_cols = config["num_features"]

    stats_queries = []
    for col in num_cols:
        stats_queries.append(
            f"SELECT\n"
            f"  '{col}' AS feature,\n"
            f"  AVG({col}) AS mean,\n"
            f"  STDDEV_SAMP({col}) AS std\n"
            f"FROM `{train_table_id}`"
        )
    union_sql = "\n  UNION ALL\n  ".join(stats_queries)

    query = f"""
    set @@query_label = 'cost:5867';

    WITH stats AS (
        {union_sql}
    ),

    json_row AS (
        SELECT TO_JSON_STRING(
            STRUCT(
                ARRAY_AGG(mean    ORDER BY feature) AS means,
                ARRAY_AGG(std     ORDER BY feature) AS stds,
                ARRAY_AGG(feature ORDER BY feature) AS cols
            )
        ) AS row
        FROM
            stats
    )

    SELECT
        row
    FROM
        json_row
    """

    client = bigquery.Client(project=project_id)
    rows = client.query(query).result()
    row = next(rows)

    def fetch_one_json(row):
        data = dict(row)
        if len(data) == 1:
            val = next(iter(data.values()))
            if isinstance(val, str):
                return json.loads(val)
        return data

    num_stats = fetch_one_json(row)

    with open(output_num_scaler.path, 'w') as f:
        json.dump(num_stats, f, ensure_ascii=False)

    gcs_path = "gs://works_ueno/preprocess_fn/v3/num_scaler.json"
    with fsspec.open(gcs_path, "w", encoding="utf-8") as f:
        json.dump(num_stats, f, ensure_ascii=False)