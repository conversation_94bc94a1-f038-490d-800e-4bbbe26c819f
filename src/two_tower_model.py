from kfp import compiler
from kfp.dsl import pipeline
from google.cloud import aiplatform
import yaml
import pathlib
from google.cloud.aiplatform import pipeline_jobs
from google_cloud_pipeline_components.v1.custom_job import create_custom_training_job_from_component
from create_user_item_table import create_user_item_table
from data_split import data_split
from create_preproessed_training_data_table import create_preprocessed_training_data_table
from append_parameters_to_configuration_file import append_parameters_to_configuration_file
from bq_to_gcs_parquet import bq_to_gcs_parquet
from train_model import train_model
from compute_normalization_parameters import compute_normalization_parameters
from compute_label_encoding_dictionary import compute_label_encoding_dictionary

if __name__ == "__main__":
    BASE_DIR = pathlib.Path(__file__).parent.parent
    PROJECT_ID_FOR_PIPELINE = 'labs-science'
    PROJECT_ID_FOR_QUERY = 'labs-science'
    LOCATION_FOR_PIPELINE = 'us-central1'
    GCS_DESTINATION_ITEM_EMBEDDING_MODEL = 'gs://works_ueno/embedding_model/v3/item_embedding_model.pt'
    GCS_DESTINATION_USER_EMBEDDING_MODEL = 'gs://works_ueno/embedding_model/v3/user_embedding_model.pt'
    PIPELINE_NAME = 'two-tower-model-v3'
    PIPELINE_ROOT = 'gs://works_ueno/two_tower_model_v3/pipeline/'
    TRAIN_TABLE_ID = 'labs-science.works_ueno.two_tower_model_sugi_v3_train_data'
    PREPROCESSED_TRAIN_TABLE_ID = 'labs-science.works_ueno.two_tower_model_sugi_train_data_preprocessed'
    BUCKET_FOR_PARQUET_FOLDER = 'works_ueno'
    GCS_FOLDER_FOR_TRAIN_DATA = 'train-sugi-parquet-v3'
    GCS_FOLDER_FOR_VALID_DATA = 'valid-sugi-parquet-v3'
    PROJECT_ID_FOR_RESULT = 'labs-science'
    DATASET_FOR_RESULT = 'works_ueno'
    BUCKET_FOR_INFERENCE = 'works_ueno'

    def load_sql(name: str) -> str:
        path = BASE_DIR / "sql" / name
        return path.read_text(encoding="utf-8")
    
    def load_yaml() -> dict:
        path = BASE_DIR / "config" / "model_config.yaml"
        with path.open(encoding="utf-8") as f:
            return yaml.safe_load(f)
    
    aiplatform.init(project=PROJECT_ID_FOR_PIPELINE, location=LOCATION_FOR_PIPELINE)

    custom_train_job = create_custom_training_job_from_component(
        train_model,
        machine_type="n1-standard-4",
        accelerator_type="NVIDIA_TESLA_T4",
        accelerator_count=1,
    )

    @pipeline(name=PIPELINE_NAME, pipeline_root=PIPELINE_ROOT)
    def two_tower_model_pipeline():
        # create_user_item_table_op = create_user_item_table(
        #     project_id=PROJECT_ID_FOR_QUERY,
        #     query=load_sql("user_item_table_temp.sql")
        # )

        # data_split_op = data_split(
        #     project_id=PROJECT_ID_FOR_QUERY,
        #     train_split_query=load_sql("split_train.sql"),
        #     valid_split_query=load_sql("split_valid.sql"),
        #     test_split_query=load_sql("split_test.sql")
        # )

        # create_preprocessed_training_data_table_op = create_preprocessed_training_data_table(
        #     project_id=PROJECT_ID_FOR_PIPELINE,
        #     query=load_sql("preprocess.sql")
        # )
        # create_preprocessed_training_data_table_op.after(data_split)

        append_parameters_to_configuration_file_op = append_parameters_to_configuration_file(
            project_id=PROJECT_ID_FOR_QUERY,
            train_table_id=TRAIN_TABLE_ID,
            config=load_yaml(),
        )
        # append_parameters_to_configuration_file_op.after(data_split)

        # bq_to_gcs_parquet_op = bq_to_gcs_parquet(
        #     project_id=PROJECT_ID_FOR_QUERY,
        #     bq_to_gcs_train_query=load_sql("bq_to_gcs_train.sql"),
        #     bq_to_gcs_valid_query=load_sql("bq_to_gcs_valid.sql")
        # )
        # bq_to_gcs_parquet_op.after(create_preprocessed_training_data_table)

        compute_normalization_parameters_op = compute_normalization_parameters(
            project_id=PROJECT_ID_FOR_QUERY,
            config=load_yaml(),
            train_table_id=TRAIN_TABLE_ID,
        )
        # compute_normalization_parameters_op.after(data_split)

        compute_label_encoding_dictionary_op = compute_label_encoding_dictionary(
            project_id=PROJECT_ID_FOR_QUERY,
            config=load_yaml(),
            train_table_id=TRAIN_TABLE_ID,
        )
        # compute_label_encoding_dictionary_op.after(data_split)

        train_model_op = custom_train_job(
            project_id=PROJECT_ID_FOR_PIPELINE,
            location=LOCATION_FOR_PIPELINE,
            input_config=append_parameters_to_configuration_file_op.outputs["output_config"],
            bucket_for_parquet_folder=BUCKET_FOR_PARQUET_FOLDER,
            gcs_folder_for_train_data=GCS_FOLDER_FOR_TRAIN_DATA,
            gcs_folder_for_valid_data=GCS_FOLDER_FOR_VALID_DATA,
            gcs_destination_item_embedding_model=GCS_DESTINATION_ITEM_EMBEDDING_MODEL,
            gcs_destination_user_embedding_model=GCS_DESTINATION_USER_EMBEDDING_MODEL,
            input_num_scaler=compute_normalization_parameters_op.outputs["output_num_scaler"],
            input_encoding_dicts=compute_label_encoding_dictionary_op.outputs["output_cat_encoding_dict"],
        )
        # train_model_op.after(bq_to_gcs_parquet_op)
    
    compiler.Compiler().compile(
        pipeline_func=two_tower_model_pipeline, package_path="two_tower_model_pipeline.json"
    )
    job = pipeline_jobs.PipelineJob(
        display_name="two-tower-model-pipeline",
        template_path="two_tower_model_pipeline.json",
        # enable_caching=False,
    )
    job.run(
        sync=False,
        service_account="<EMAIL>"
    )


