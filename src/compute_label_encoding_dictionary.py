from kfp.dsl import (component, Dataset, Input, Output)

@component(
    packages_to_install=[
        "pyyaml",
        "google-cloud-bigquery",
        "gcsfs", 
    ],
    base_image="python:3.9",
)
def compute_label_encoding_dictionary(
    project_id: str,
    config: dict,
    train_table_id: str,
    output_cat_encoding_dict: Output[Dataset],
):
    from google.cloud import bigquery
    import json
    import fsspec

    cat_cols = config["cat_features"]

    cte_sqls = []
    for col in cat_cols:
        cte_sqls.append(
            f"""{col} AS (
            SELECT
                {col} AS category,
                ML.LABEL_ENCODER({col}, 1000000, 1) OVER() AS code
            FROM
                `{train_table_id}`
            WHERE {col} IS NOT NULL
            GROUP BY {col}
            )"""
        )
    ctes = ",\n".join(cte_sqls)

    field_sqls = []
    for col in cat_cols:
        field_sqls.append(
            f"""
            '"{col}":',
            (
                SELECT
                    CONCAT('{{', STRING_AGG(FORMAT('"%s":%d', category, code) ORDER BY category), '}}')
                FROM
                    {col}
            )
            """
        )
    json_body = ",',', \n".join(field_sqls)

    query = f"""
    set @@query_label = 'cost:5867';

    WITH
        {ctes}

    SELECT
        CONCAT(
            '{{',
            {json_body},
            '}}') AS row
    """
    client = bigquery.Client(project=project_id)
    rows = client.query(query).result()
    row = next(rows)

    def fetch_one_json(row):
        data = dict(row)
        if len(data) == 1:
            val = next(iter(data.values()))
            if isinstance(val, str):
                return json.loads(val)
        return data

    cat_encoding_dict = fetch_one_json(row)

    with open(output_cat_encoding_dict.path, 'w') as f:
        json.dump(cat_encoding_dict, f, ensure_ascii=False)
    
    gcs_path = "gs://works_ueno/preprocess_fn/v3/cat_encoding_dict.json"
    with fsspec.open(gcs_path, "w", encoding="utf-8") as f:
        json.dump(cat_encoding_dict, f, ensure_ascii=False)

