from kfp.dsl import component

@component(
    packages_to_install=[
        "pyyaml",
        "google-cloud-bigquery",
    ],
    base_image="python:3.9",
)
def data_split (
    project_id: str,
    train_split_query: str,
    valid_split_query: str,
    test_split_query: str       
):
    from google.cloud import bigquery

    client = bigquery.Client(project=project_id)
    
    job_train_split = client.query(train_split_query)
    job_train_split.result()

    job_valid_split = client.query(valid_split_query)
    job_valid_split.result()

    job_test_split = client.query(test_split_query)
    job_test_split.result()


