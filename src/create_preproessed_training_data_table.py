from kfp.dsl import (component, Dataset, Input, Output)

@component(
    packages_to_install=[
        "pyyaml",
        "google-cloud-bigquery",
    ],
    base_image="python:3.9",
)
def create_preprocessed_training_data_table(
    project_id: str,
    query: str,
):
    import yaml
    from google.cloud import bigquery

    # v3

    client = bigquery.Client(project=project_id)
    job = client.query(query)
    job.result()
