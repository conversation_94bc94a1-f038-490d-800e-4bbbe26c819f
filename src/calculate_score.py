if __name__ == "__main__":
    from google.cloud import bigquery
    import pathlib

    BASE_DIR = pathlib.Path(__file__).parent.parent

    def load_sql(name: str) -> str:
        path = BASE_DIR / "sql" / name
        return path.read_text(encoding="utf-8")

    project_id = 'labs-science'
    client = bigquery.Client(project=project_id)

    score_purchase_flag_query=load_sql("score_purchase_flag.sql")
    
    job = client.query(score_purchase_flag_query)
    job.result()
    