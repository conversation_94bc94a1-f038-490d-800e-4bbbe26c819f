"""
Script to extract preprocessing parameters from your trained model artifacts
and generate BigQuery SQL with actual values.
"""

import json
import yaml
from google.cloud import storage
import fsspec

def download_preprocessing_artifacts():
    """
    Download the preprocessing artifacts from GCS.
    """
    # Download normalization parameters
    num_scaler_path = "gs://works_ueno/preprocess_fn/v3/num_scaler.json"
    cat_encoding_path = "gs://works_ueno/preprocess_fn/v3/cat_encoding_dict.json"
    
    with fsspec.open(num_scaler_path, "r", encoding="utf-8") as f:
        num_scaler = json.load(f)
    
    with fsspec.open(cat_encoding_path, "r", encoding="utf-8") as f:
        cat_encoding = json.load(f)
    
    return num_scaler, cat_encoding

def generate_normalization_sql(num_scaler, feature_cols):
    """
    Generate SQL for numerical feature normalization.
    """
    means = num_scaler['means']
    stds = num_scaler['stds']
    cols = num_scaler['cols']
    
    # Create mapping from column name to mean/std
    norm_params = {}
    for i, col in enumerate(cols):
        norm_params[col] = {'mean': means[i], 'std': stds[i]}
    
    sql_lines = []
    for col in feature_cols:
        if col in norm_params:
            mean_val = norm_params[col]['mean']
            std_val = norm_params[col]['std']
            sql_lines.append(f"    ({col} - {mean_val}) / {std_val} AS {col}_norm,")
        else:
            print(f"Warning: No normalization parameters found for {col}")
    
    return "\n".join(sql_lines)

def generate_encoding_sql(cat_encoding, feature_cols):
    """
    Generate SQL for categorical feature encoding.
    """
    sql_lines = []
    
    for col in feature_cols:
        if col in cat_encoding:
            mapping = cat_encoding[col]
            sql_lines.append(f"    CASE")
            
            # Sort by value to ensure consistent ordering
            for category, code in sorted(mapping.items(), key=lambda x: x[1]):
                sql_lines.append(f"      WHEN {col} = '{category}' THEN {code}")
            
            sql_lines.append(f"      ELSE 0  -- Unknown/padding value")
            sql_lines.append(f"    END AS {col}_encoded,")
            sql_lines.append("")
        else:
            print(f"Warning: No encoding found for {col}")
    
    return "\n".join(sql_lines)

def generate_feature_arrays_sql(config):
    """
    Generate SQL for creating feature arrays in the correct order.
    """
    user_num_cols = config['model_config']['user_num_cols']
    user_cat_cols = config['model_config']['user_cat_cols']
    item_num_cols = config['model_config']['item_num_cols']
    item_cat_cols = config['model_config']['item_cat_cols']
    
    # User numerical features array
    user_num_array = "    [\n"
    for col in user_num_cols:
        user_num_array += f"      {col}_norm,\n"
    user_num_array = user_num_array.rstrip(',\n') + "\n    ] AS user_numerical_features,"
    
    # User categorical features array
    user_cat_array = "    [\n"
    for col in user_cat_cols:
        user_cat_array += f"      {col}_encoded,\n"
    user_cat_array = user_cat_array.rstrip(',\n') + "\n    ] AS user_categorical_features,"
    
    # Item numerical features array
    item_num_array = "    [\n"
    for col in item_num_cols:
        item_num_array += f"      {col}_norm,\n"
    item_num_array = item_num_array.rstrip(',\n') + "\n    ] AS item_numerical_features,"
    
    # Item categorical features array
    item_cat_array = "    [\n"
    for col in item_cat_cols:
        item_cat_array += f"      {col}_encoded,\n"
    item_cat_array = item_cat_array.rstrip(',\n') + "\n    ] AS item_categorical_features"
    
    return user_num_array, user_cat_array, item_num_array, item_cat_array

def generate_complete_sql():
    """
    Generate the complete BigQuery SQL with actual preprocessing parameters.
    """
    # Load config
    with open("config/model_config.yaml", 'r') as f:
        config = yaml.safe_load(f)
    
    # Download preprocessing artifacts
    try:
        num_scaler, cat_encoding = download_preprocessing_artifacts()
    except Exception as e:
        print(f"Error downloading artifacts: {e}")
        print("Using placeholder values. Please update with actual values.")
        return
    
    # Generate SQL components
    user_num_cols = config['model_config']['user_num_cols']
    user_cat_cols = config['model_config']['user_cat_cols']
    item_num_cols = config['model_config']['item_num_cols']
    item_cat_cols = config['model_config']['item_cat_cols']
    
    num_norm_sql = generate_normalization_sql(num_scaler, user_num_cols + item_num_cols)
    cat_encoding_sql = generate_encoding_sql(cat_encoding, user_cat_cols + item_cat_cols)
    
    user_num_array, user_cat_array, item_num_array, item_cat_array = generate_feature_arrays_sql(config)
    
    # Complete SQL template
    complete_sql = f"""
-- BigQuery SQL for ONNX model inference with actual preprocessing parameters
-- Generated automatically from training artifacts

WITH feature_preprocessing AS (
  SELECT 
    adid,
    
    -- Numerical feature normalization
{num_norm_sql}
    
    -- Categorical feature encoding
{cat_encoding_sql}
    
  FROM `your_project.your_dataset.raw_user_item_data`
),

model_inputs AS (
  SELECT 
    adid,
    
    -- Feature arrays for ONNX model input
{user_num_array}
    
{user_cat_array}
    
{item_num_array}
    
{item_cat_array}
    
  FROM feature_preprocessing
)

-- Get embeddings using ONNX models
SELECT 
  adid,
  
  ML.PREDICT(
    MODEL `your_project.your_dataset.user_embedding_onnx_model`,
    (SELECT AS STRUCT 
       user_numerical_features AS numerical_features,
       user_categorical_features AS categorical_features)
  ).user_embedding AS user_embedding,
  
  ML.PREDICT(
    MODEL `your_project.your_dataset.item_embedding_onnx_model`,
    (SELECT AS STRUCT 
       item_numerical_features AS numerical_features,
       item_categorical_features AS categorical_features)
  ).item_embedding AS item_embedding

FROM model_inputs;
"""
    
    # Save to file
    with open("sql/bigquery_onnx_inference_complete.sql", "w") as f:
        f.write(complete_sql)
    
    print("Complete SQL generated and saved to sql/bigquery_onnx_inference_complete.sql")
    print("\nFeature summary:")
    print(f"User numerical features: {len(user_num_cols)}")
    print(f"User categorical features: {len(user_cat_cols)}")
    print(f"Item numerical features: {len(item_num_cols)}")
    print(f"Item categorical features: {len(item_cat_cols)}")

if __name__ == "__main__":
    generate_complete_sql()
