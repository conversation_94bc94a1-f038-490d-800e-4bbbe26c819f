import apache_beam as beam
import torch

class PreprocessDoFn(beam.DoFn):
    def __init__(self, mean, std, cat2idx, config):
        super().__init__()
        self.mean    = torch.tensor(mean, dtype=torch.float32)
        self.std     = torch.tensor(std,  dtype=torch.float32)
        self.cat2idx = cat2idx
        self.cat_cols = config['model_config']['item_cat_cols']
        self.num_cols = config['model_config']['item_num_cols']

    def process(self, row):
        key = row['upper2_nm']
        numeric_values = [row[col] for col in self.num_cols]
        numeric = torch.tensor(numeric_values, dtype=torch.float32)
        num_norm = (numeric - self.mean) / self.std
        idxs = []
        for col in self.cat_cols:
            v       = row[col]
            mapping = self.cat2idx[col]
            idx     = mapping.get(v, 0)
            idxs.append(idx)
        cat_tensor = torch.tensor(idxs, dtype=torch.long)
        yield key, (num_norm, cat_tensor)