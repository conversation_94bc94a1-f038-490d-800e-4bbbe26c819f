from beam_transforms import PreprocessDoFn
import gcsfs
import json
import yaml

if __name__ == "__main__":
    project = "labs-science"
    dataset = "works_ueno"
    bucket_name = "works_ueno"

    item_name="laundry_goods"
    input_item_table = f'two_tower_model_sugi_{item_name}_data'
    item_embedding_output_table_schema = "category:STRING, item_embedding:STRING"
    input_item_table_spec = f'{project}:{dataset}.{input_item_table}'
    item_embedding_output_table = f"{project}:{dataset}.two_tower_model_sugi_v3_{item_name}_embedding"

    input_user_table = 'two_tower_model_sugi_test_data_handle_missing'
    user_embedding_output_table_schema = "adid:STRING, user_embedding:STRING"
    input_user_table_spec = f'{project}:{dataset}.{input_user_table}'
    user_embedding_output_table = f"{project}:{dataset}.two_tower_model_sugi_v3_user_embedding"

    fs = gcsfs.GCSFileSystem(token=None)

    import pathlib
    BASE_DIR = pathlib.Path(__file__).parent.parent
    def load_yaml() -> dict:
        path = BASE_DIR / "config" / "model_config.yaml"
        with path.open(encoding="utf-8") as f:
            return yaml.safe_load(f)
    config = load_yaml()

    num_scaler_path = "gs://works_ueno/preprocess_fn/v3/num_scaler.json"
    encoding_dict_path = "gs://works_ueno/preprocess_fn/v3/cat_encoding_dict.json"
    scripted_item_embedding_model_gcs_path = "gs://works_ueno/two_tower_model_v3/pipeline/261231409699/two-tower-model-v3-20250604065851/train-model_-2347008277881028608/output_item_embedding_model.pt"

    with fs.open(encoding_dict_path, "r", encoding="utf-8") as f:
        encoding_dicts = json.load(f)

    with fs.open(num_scaler_path, 'r') as f:
        num_scaler = json.load(f)

    from apache_beam.options.pipeline_options import PipelineOptions, SetupOptions
    import apache_beam as beam
    from apache_beam.ml.inference.pytorch_inference import PytorchModelHandlerTensor
    from apache_beam.ml.inference.base import KeyedModelHandler
    from apache_beam.io.gcp.bigquery import ReadFromBigQuery
    from apache_beam.io import WriteToBigQuery, BigQueryDisposition
    from apache_beam.ml.inference.base import PredictionResult
    from apache_beam.ml.inference.base import RunInference
    import torch
    from google.cloud import storage

    all_cols   = num_scaler['cols']
    means_all  = num_scaler['means']
    stds_all   = num_scaler['stds']
    num_cols = config['model_config']['item_num_cols']  # e.g. ['price']
    means = [means_all[all_cols.index(c)] for c in num_cols]
    stds  = [stds_all[all_cols.index(c)]  for c in num_cols]

    def inference_fn(batch, model, *args, **kwargs):
        num_batch = torch.stack([ex[0] for ex in batch], dim=0)
        cat_batch = torch.stack([ex[1] for ex in batch], dim=0)
        return model(num_batch, cat_batch)

    item_embedding_model_handler = PytorchModelHandlerTensor(
        torch_script_model_path=scripted_item_embedding_model_gcs_path,
        device='CPU',
        inference_fn = inference_fn
    )

    pipeline_options = PipelineOptions().from_dictionary({
        # 'runner': 'DataflowRunner',
        # 'project': project,
        # 'region': 'us-central1',
        'temp_location': f'gs://{bucket_name}/tmp',
        'staging_location': f'gs://{bucket_name}/staging',
        # 'worker_machine_type': 'n1-standard-4',
        # 'num_workers': 1,
        # 'requirements_file': './requirements.txt'
    })

    keyed_item_embedding_model_handler = KeyedModelHandler(item_embedding_model_handler)

    def to_bq_row(rec):
        key, embedding = rec  # (key, PredictionResult)
        return {
            "category": key,
            "item_embedding": json.dumps(embedding.cpu().tolist()),
        }

    with beam.Pipeline(options=pipeline_options) as p:
        (
        p
        | "ReadFromBQ"   >> beam.io.ReadFromBigQuery(table=input_item_table_spec, method="DIRECT_READ")
        | "Preprocess"  >> beam.ParDo(PreprocessDoFn(means, stds, encoding_dicts, config))
        | "RunInference" >> RunInference(model_handler=keyed_item_embedding_model_handler)
        | "FormatForBQ"  >> beam.Map(to_bq_row)
        | "WriteToBQ"    >> WriteToBigQuery(
                            table                = item_embedding_output_table,
                            schema               = item_embedding_output_table_schema,
                            create_disposition   = BigQueryDisposition.CREATE_IF_NEEDED,
                            write_disposition    = BigQueryDisposition.WRITE_TRUNCATE,
                            method               = WriteToBigQuery.Method.STORAGE_WRITE_API,
                            )
        )

