if __name__ == "__main__":
    project = "labs-science"
    dataset = "works_ueno"
    bucket_name = "works_ueno"

    input_user_table = 'two_tower_model_sugi_v3_test_user'
    user_embedding_output_table_schema = "adid:STRING, user_embedding:STRING"
    input_user_table_spec = f'{project}:{dataset}.{input_user_table}'
    from google.cloud import bigquery
    client = bigquery.Client(project=project)
    query = f"SELECT COUNT(1) AS cnt FROM `{project}.{dataset}.{input_user_table}`"
    job = client.query(query)
    total_rows = int(list(job.result())[0].cnt)
    print(f"[INFO] Total rows in table = {total_rows}")
    user_embedding_output_table = f"{project}:{dataset}.two_tower_model_sugi_v3_user_embedding"

    import gcsfs
    import json
    import yaml

    fs = gcsfs.GCSFileSystem(token=None)

    import pathlib
    BASE_DIR = pathlib.Path(__file__).parent.parent
    def load_yaml() -> dict:
        path = BASE_DIR / "config" / "model_config.yaml"
        with path.open(encoding="utf-8") as f:
            return yaml.safe_load(f)
    config = load_yaml()

    num_scaler_path = "gs://works_ueno/preprocess_fn/v3/num_scaler.json"
    encoding_dict_path = "gs://works_ueno/preprocess_fn/v3/cat_encoding_dict.json"
    scripted_user_embedding_model_gcs_path = "gs://works_ueno/two_tower_model_v3/pipeline/261231409699/two-tower-model-v3-20250604065851/train-model_-2347008277881028608/output_user_embedding_model.pt"

    with fs.open(encoding_dict_path, "r", encoding="utf-8") as f:
        encoding_dicts = json.load(f)

    with fs.open(num_scaler_path, 'r') as f:
        num_scaler = json.load(f)

    from apache_beam.options.pipeline_options import PipelineOptions, StandardOptions, DirectOptions
    import apache_beam as beam
    from apache_beam.ml.inference.pytorch_inference import PytorchModelHandlerTensor
    from apache_beam.ml.inference.base import KeyedModelHandler
    from apache_beam.io.gcp.bigquery import ReadFromBigQuery
    from apache_beam.io import WriteToBigQuery, BigQueryDisposition
    from apache_beam.ml.inference.base import PredictionResult
    from apache_beam.ml.inference.base import RunInference
    import torch
    from google.cloud import storage
    import time

    all_cols   = num_scaler['cols']
    means_all  = num_scaler['means']
    stds_all   = num_scaler['stds']
    num_cols = config['model_config']['user_num_cols']
    means = [means_all[all_cols.index(c)] for c in num_cols]
    stds  = [stds_all[all_cols.index(c)]  for c in num_cols]

    def inference_fn(batch, model, *args, **kwargs):
        num_batch = torch.stack([ex[0] for ex in batch], dim=0)
        cat_batch = torch.stack([ex[1] for ex in batch], dim=0)
        return model(num_batch, cat_batch)

    user_embedding_model_handler = PytorchModelHandlerTensor(
        torch_script_model_path=scripted_user_embedding_model_gcs_path,
        device='CPU',
        inference_fn = inference_fn
    )

    opts = PipelineOptions()
    opts.view_as(StandardOptions).runner = 'DirectRunner'
    opts.view_as(DirectOptions).direct_num_workers = 1

    pipeline_options = opts
    keyed_item_embedding_model_handler = KeyedModelHandler(user_embedding_model_handler)

    def to_bq_row(rec):
        key, embedding = rec  # (key, PredictionResult)
        return {
            "adid": key,
            "user_embedding": json.dumps(embedding.cpu().tolist()),
        }

    class ProgressDoFn(beam.DoFn):
        def __init__(self, total_rows: int, log_every: int = 10000):
            super().__init__()
            self.total_rows = total_rows
            self.log_every = log_every
            self.start_time = None
            self.processed = 0

        def process(self, element):
            if self.start_time is None:
                self.start_time = time.time()
            self.processed += 1
            if self.processed % self.log_every == 0:
                elapsed = time.time() - self.start_time
                percent = self.processed / self.total_rows * 100
                speed = self.processed / elapsed if elapsed > 0 else 0
                remaining = ((self.total_rows - self.processed) / speed) if speed > 0 else float('inf')
                print(f"[Progress] {self.processed}/{self.total_rows} rows ({percent:.1f}%), "
                      f"elapsed: {elapsed:.1f}s, remaining: {remaining:.1f}s")
            yield element

    class PreprocessDoFn(beam.DoFn):
        def __init__(self, mean, std, cat2idx, config):
            super().__init__()
            self.mean    = torch.tensor(mean, dtype=torch.float32)
            self.std     = torch.tensor(std,  dtype=torch.float32)
            self.cat2idx = cat2idx
            self.cat_cols = config['model_config']['user_cat_cols']
            self.num_cols = config['model_config']['user_num_cols']
            self.missing_cols_1 = config['model_config']['missing_cols_1']

        def process(self, row):
            key = row['adid']

            numeric_values = []
            for col in self.num_cols:
                v = row.get(col)
                if v is None:
                    v = 0.0
                numeric_values.append(v)
            
            numeric = torch.tensor(numeric_values, dtype=torch.float32)
            num_norm = (numeric - self.mean) / self.std
            idxs = []
            for col in self.cat_cols:
                v       = row[col]
                mapping = self.cat2idx[col]
                idx     = mapping.get(v, 0)
                idxs.append(idx)
            cat_tensor = torch.tensor(idxs, dtype=torch.long)
            yield key, (num_norm, cat_tensor)

    with beam.Pipeline(options=pipeline_options) as p:
        (
        p
        | "ReadFromBQ"   >> beam.io.ReadFromBigQuery(table=input_user_table_spec, method="DIRECT_READ")
        | "CountRows"    >> beam.ParDo(ProgressDoFn(total_rows, log_every=10000))
        | "Preprocess"   >> beam.ParDo(PreprocessDoFn(means, stds, encoding_dicts, config))
        | "Reshuffle"    >> beam.Reshuffle()
        | "RunInference" >> RunInference(
            model_handler=keyed_item_embedding_model_handler,
            batch_size=16)
        | "FormatForBQ"  >> beam.Map(to_bq_row)
        | "WriteToBQ"    >> WriteToBigQuery(
                            table                = user_embedding_output_table,
                            schema               = user_embedding_output_table_schema,
                            create_disposition   = BigQueryDisposition.CREATE_IF_NEEDED,
                            write_disposition    = BigQueryDisposition.WRITE_TRUNCATE,
                            method               = WriteToBigQuery.Method.STORAGE_WRITE_API,
                            )
        )

