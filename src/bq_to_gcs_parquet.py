from kfp.dsl import (component)

@component(
    packages_to_install=[
        "pyyaml",
        "google-cloud-bigquery",
    ],
    base_image="python:3.9",
)
def bq_to_gcs_parquet(
    project_id: str,
    bq_to_gcs_train_query: str,
    bq_to_gcs_valid_query: str,
):
    from google.cloud import bigquery

    client = bigquery.Client(project=project_id)
    
    job_train_split = client.query(bq_to_gcs_train_query)
    job_train_split.result()

    job_valid_split = client.query(bq_to_gcs_valid_query)
    job_valid_split.result()


