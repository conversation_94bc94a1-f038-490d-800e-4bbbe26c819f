from kfp.dsl import (component, Dataset, Output)

@component(
    packages_to_install=[
        "pyyaml",
        "google-cloud-bigquery",
    ],
    base_image="python:3.9",
)
def append_parameters_to_configuration_file(
    project_id: str,
    train_table_id: str,
    config: dict,
    output_config: Output[Dataset]
):
    import yaml
    import copy
    import json
    from google.cloud import bigquery

    def get_embedding_dimension(dim: int, max_embedding_size: int = 50) -> tuple[int, int]:
        num_categories = dim + 1
        embedding_size = min(
            max_embedding_size,
            round(1.6 * dim ** 0.56)
        )
        return (num_categories, embedding_size)

    cat_cols = config["cat_features"]

    cat_count_items = [f"COUNT(DISTINCT {c}) AS {c}" for c in cat_cols]
    cat_count_select = ",\n".join(cat_count_items)

    cat_items = [f"{c} as {c}" for c in cat_cols]
    cat_select = ",\n".join(cat_items)
    query = f"""
    set @@query_label = 'cost:5867';
    
    WITH category_count AS (
        SELECT
            {cat_count_select}
        FROM
            `{train_table_id}`
    )

    select
        to_json_string(
            struct(
                {cat_select}
            )
        ) as row
    from
        category_count
    """

    client = bigquery.Client(project=project_id)
    rows = client.query(query).result()
    row = next(rows)

    def fetch_one_json(row):
        data = dict(row)
        if len(data) == 1:
            val = next(iter(data.values()))
            if isinstance(val, str):
                return json.loads(val)
        return data

    cat_count = fetch_one_json(row)

    for col in config['cat_features']:
        dim = cat_count[col]
        config['model_config'][col] = get_embedding_dimension(dim=dim)

    config['model_config']["item_cat_dims"] = [
        config['model_config'][c]
        for c in config['model_config']["item_cat_cols"]
    ]
    config['model_config']["user_cat_dims"] = [
        config['model_config'][c]
        for c in config['model_config']["user_cat_cols"]
    ]

    class NoAliasDumper(yaml.SafeDumper):
        def ignore_aliases(self, data):
            return True

    config_copy = copy.deepcopy(config)
    with open(output_config.path + ".yml", 'w', encoding='utf-8') as f:
        yaml.dump(
            config_copy,
            f,
            Dumper=NoAliasDumper,
            allow_unicode=True,
            default_flow_style=True,
            sort_keys=False
        )
