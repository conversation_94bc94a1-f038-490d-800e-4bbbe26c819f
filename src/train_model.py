from kfp.dsl import (component, Dataset, Model, Input, Output, Metrics)

@component(
    packages_to_install=[
        "pandas",
        "torch==1.13.1",
        "numpy==1.21.6",
        "pyyaml",
        "google-cloud-bigquery",
        "db-dtypes",
        "pandas",
        "google-cloud-storage",
        "pyarrow",
        "gcsfs",
        "google-cloud-aiplatform",
    ],
    base_image="python:3.9",
)
def train_model(
    project_id: str,
    location: str,
    input_config: Input[Dataset],
    bucket_for_parquet_folder: str,
    gcs_folder_for_train_data: str,
    gcs_folder_for_valid_data: str,
    gcs_destination_item_embedding_model: str,
    gcs_destination_user_embedding_model: str,
    input_num_scaler: Input[Dataset],
    input_encoding_dicts: Input[Dataset],
    output_item_embedding_model: Output[Model],
    output_user_embedding_model: Output[Model],
    test_output: Output[Dataset],
    summary_metrics: Output[Metrics],
):
    import os
    import torch
    from torch.utils.data import IterableDataset, DataLoader
    import torch.nn as nn
    import torch.optim as optim
    import yaml
    import json
    import numpy as np
    from google.cloud import bigquery
    import pandas as pd
    import random
    from google.cloud import storage
    import pyarrow.parquet as pq
    import gcsfs
    from collections import defaultdict
    from typing import List
    import time
    import math
    from google.cloud import aiplatform
    import torch.nn.functional as F
    import pyarrow as pa

    class TrainDataset(IterableDataset):
        def __init__(self, files, config):
            super().__init__()
            self.files = files
            self.config = config
            self.item_num_cols = self.config['model_config']['item_num_cols']
            self.item_cat_cols = self.config['model_config']['item_cat_cols']
            self.user_num_cols = self.config['model_config']['user_num_cols']
            self.user_cat_cols = self.config['model_config']['user_cat_cols']

        def __iter__(self):
            files = self.files.copy()
            random.shuffle(files)
            buffer = []
            buffer_size = 16
            for path in files:
                print(path, flush=True)
                cols_data = defaultdict(list)
                with fs.open(path, 'rb') as f:
                    table = pq.ParquetFile(f).read()
                schema_names = table.schema.names
                for name, col in zip(schema_names, table.columns):
                    if (
                        name in self.item_num_cols
                        or name in self.item_cat_cols
                        or name in self.user_num_cols
                        or name in self.user_cat_cols
                        or name == "sum_quantity"
                        or name == "label"
                    ):
                        if pa.types.is_integer(col.type) or pa.types.is_floating(col.type):
                            data = col.to_numpy()
                        elif pa.types.is_decimal(col.type):
                            data = col.cast(pa.float64()).to_numpy()
                        else:
                            print(f'Skipping column {name} with type {col.type}', flush=True)
                            print(f"Skipping column {name} with type {col.type}", flush=True)
                            continue
                        cols_data[name].append(data)
                col_tensor = {
                    name: torch.from_numpy(np.concatenate(arrs, axis=0)).to(torch.float32)
                    for name, arrs in cols_data.items()
                }
                M = next(iter(col_tensor.values())).size(0)
                for i in range(M):
                    example = {
                        'item_num_feature': torch.stack([col_tensor[c][i] for c in self.item_num_cols], dim=0),
                        'item_cat_feature': torch.stack([col_tensor[c][i] for c in self.item_cat_cols], dim=0).long(),
                        'user_num_feature': torch.stack([col_tensor[c][i] for c in self.user_num_cols], dim=0),
                        'user_cat_feature': torch.stack([col_tensor[c][i] for c in self.user_cat_cols], dim=0).long(),
                    }
                    example['sum_quantity'] = col_tensor['sum_quantity'][i]
                    example['label'] = col_tensor['label'][i]
                    buffer.append(example)
                    if len(buffer) >= buffer_size:
                        idx = random.randrange(len(buffer))
                        yield buffer.pop(idx)
            random.shuffle(buffer)
            for example in buffer:
                yield example
    
    class ValidDataset(IterableDataset):
        def __init__(self, files, config, num_scaler, encoding_dicts):
            super().__init__()
            self.files = files
            self.config = config
            self.item_num_cols = self.config['model_config']['item_num_cols']
            self.item_cat_cols = self.config['model_config']['item_cat_cols']
            self.user_num_cols = self.config['model_config']['user_num_cols']
            self.user_cat_cols = self.config['model_config']['user_cat_cols']
            self.num_means = num_scaler['means']
            self.num_stds = num_scaler['stds']
            self.num_cols = num_scaler['cols']
            self.encoding_dicts = encoding_dicts

        def __iter__(self):
            files = self.files.copy()
            random.shuffle(files)
            buffer = []
            buffer_size = 16
            for path in files:
                print(path, flush=True)
                cols_data = defaultdict(list)
                with fs.open(path, 'rb') as f:
                    table = pq.ParquetFile(f).read()
                schema_names = table.schema.names
                for name, col in zip(schema_names, table.columns):
                    if (name in self.item_num_cols or name in self.item_cat_cols or name in self.user_num_cols or name in self.user_cat_cols or name == "label"):
                        cols_data[name].append(col.to_numpy())
                col_tensor = {
                    name: torch.from_numpy(np.concatenate(arrs, axis=0)).to(torch.float32)
                    for name, arrs in cols_data.items()
                }
                M = next(iter(col_tensor.values())).size(0)
                for i in range(M):
                    example = {
                        'item_num_feature': torch.stack([col_tensor[c][i] for c in self.item_num_cols], dim=0),
                        'item_cat_feature': torch.stack([col_tensor[c][i] for c in self.item_cat_cols], dim=0).long(),
                        'user_num_feature': torch.stack([col_tensor[c][i] for c in self.user_num_cols], dim=0),
                        'user_cat_feature': torch.stack([col_tensor[c][i] for c in self.user_cat_cols], dim=0).long(),
                    }
                    example['label'] = col_tensor['label'][i]
                    buffer.append(example)
                    if len(buffer) >= buffer_size:
                        idx = random.randrange(len(buffer))
                        yield buffer.pop(idx)
            random.shuffle(buffer)
            for example in buffer:
                yield example

    class ItemEmbeddingModel(nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config

            # カテゴリ特徴量のカラム数だけエンベディング層をつくる
            # １次元（カテゴリ特徴量のインデックス、０以上x未満である必要がある） -> エンベディング層 -> y次元のエンベディング
            # nn.ModuleListは複数の層をまとめる
            self.cat_emb = nn.ModuleList([
                nn.Embedding(x, y, padding_idx=0) for x, y in config["model_config"]["item_cat_dims"]
            ])

            # カテゴリ特徴量の埋め込み層の出力次元を全て足し合わせる
            n_cat_out = sum(
                y for x, y in config["model_config"]["item_cat_dims"])

            # nn.Sequential -> 中に並べたモジュールを順番に実行
            # nn.Linear(a, b) -> a次元からb次元に線形変換
            # nn.LayerNorm(a) -> a次元のベクトルを各次元ごとに標準化
            self.cat_proj = nn.Sequential(nn.Linear(n_cat_out, config["model_config"]["item_cat_hidden_size"]),
                                        nn.LayerNorm(config["model_config"]["item_cat_hidden_size"]))

            self._init_weight(self.cat_proj)

            # nn.BatchNorm1d -> 入力の数値特徴量ベクトルを正規化
            # nn.Linear(a, b) -> a次元からb次元へ線形変換
            self.num_emb = nn.Sequential(
                nn.BatchNorm1d(len(config["model_config"]["item_num_cols"])),
                nn.Linear(len(config["model_config"]["item_num_cols"]),
                        config["model_config"]["item_num_hidden_size"]),
                nn.BatchNorm1d(config["model_config"]["item_num_hidden_size"]),
                nn.LeakyReLU(),
                nn.Linear(config["model_config"]["item_num_hidden_size"],
                        config["model_config"]["item_num_hidden_size"]),
                nn.BatchNorm1d(config["model_config"]["item_num_hidden_size"]),
                nn.LeakyReLU(),
            )

            self._init_weight(self.num_emb)

            head_h = config["model_config"]["item_cat_hidden_size"] + \
                config["model_config"]["item_num_hidden_size"]

            self.head = nn.Sequential(
                nn.Linear(head_h, head_h),
                nn.BatchNorm1d(head_h),
                nn.LeakyReLU(),
                nn.Linear(head_h, config["model_config"]
                        ["emb_size"]),  # emb_sizeの次元で埋め込む
            )

            self._init_weight(self.head)

        def _init_weight(self, mod):
            """
            各層（nn.Linearやnn.LayerNormなど）の学習パラメーター（重みやバイアス）を適切な初期値で設定する
            """
            if isinstance(mod, nn.Linear):
                mod.weight.data.normal_(0.0)
                if mod.bias is not None:
                    mod.bias.data.zero_()
            elif isinstance(mod, nn.LayerNorm):
                mod.bias.data.zero_()
                mod.weight.data.fill_(1.0)

        @torch.jit.export
        def combine_cat_embeddings(self, cat_features: torch.Tensor) -> torch.Tensor:
            embs: List[torch.Tensor] = []
            for idx, emb in enumerate(self.cat_emb):
                embs.append(emb(cat_features[:, idx]))
            return torch.cat(embs, dim=1)

        def forward(self, num_features, cat_features):
            num_embs = self.num_emb(num_features)
            cat_embs = self.combine_cat_embeddings(cat_features)
            cat_embs = self.cat_proj(cat_embs)
            x = torch.cat((num_embs, cat_embs), dim=1)
            out = self.head(x)
            return out / out.norm(p=2, dim=-1, keepdim=True)

    class UserEmbeddingModel(nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config
            self.cat_emb = nn.ModuleList([
                nn.Embedding(x, y, padding_idx=0)
                for x, y in config["model_config"]["user_cat_dims"]
            ])
            n_cat_out = sum(
                y for x, y in config["model_config"]["user_cat_dims"])
            self.cat_proj = nn.Sequential(nn.Linear(n_cat_out, config["model_config"]["user_cat_hidden_size"]),
                                        nn.LayerNorm(config["model_config"]["user_cat_hidden_size"]))
            self._init_weight(self.cat_proj)
            self.num_emb = nn.Sequential(
                nn.BatchNorm1d(len(config["model_config"]["user_num_cols"])),
                nn.Linear(len(config["model_config"]["user_num_cols"]),
                        config["model_config"]["user_num_hidden_size"]),
                nn.BatchNorm1d(config["model_config"]["user_num_hidden_size"]),
                nn.LeakyReLU(),
                nn.Linear(config["model_config"]["user_num_hidden_size"],
                        config["model_config"]["user_num_hidden_size"]),
                nn.BatchNorm1d(config["model_config"]["user_num_hidden_size"]),
                nn.LeakyReLU(),
            )
            self._init_weight(self.num_emb)
            head_h = config["model_config"]["user_cat_hidden_size"] + \
                config["model_config"]["user_num_hidden_size"]
            self.head = nn.Sequential(
                nn.Linear(head_h, head_h),
                nn.BatchNorm1d(head_h),
                nn.LeakyReLU(),
                nn.Linear(head_h, config["model_config"]["emb_size"]),
            )
            self._init_weight(self.head)

        def _init_weight(self, mod):
            if isinstance(mod, nn.Linear):
                mod.weight.data.normal_(0.0)
                if mod.bias is not None:
                    mod.bias.data.zero_()
            elif isinstance(mod, nn.LayerNorm):
                mod.bias.data.zero_()
                mod.weight.data.fill_(1.0)

        @torch.jit.export
        def combine_cat_embeddings(self, cat_features: torch.Tensor) -> torch.Tensor:
            embs: List[torch.Tensor] = []
            for idx, emb in enumerate(self.cat_emb):
                embs.append(emb(cat_features[:, idx]))
            return torch.cat(embs, dim=1)

        def forward(self, num_features, cat_features):
            num_embs = self.num_emb(num_features)
            cat_embs = self.combine_cat_embeddings(cat_features)
            cat_embs = self.cat_proj(cat_embs)
            x = torch.cat((num_embs, cat_embs), dim=1)
            out = self.head(x)
            return out / out.norm(p=2, dim=-1, keepdim=True)

    class TwoTowerModel(nn.Module):
        def __init__(self, config):
            super().__init__()
            self.item_emb = ItemEmbeddingModel(config)
            self.user_emb = UserEmbeddingModel(config)
            self.logit_scale = nn.Parameter(
                torch.ones([]) * 2.5)  # 学習可能なスカラーを初期値2.5で持つ

        def forward(self, inputs):
            item_e = self.item_emb(inputs["item_num_feature"], inputs["item_cat_feature"])
            user_e = self.user_emb(inputs["user_num_feature"], inputs["user_cat_feature"])
            scale = self.logit_scale.exp()
            logits = (user_e * item_e).sum(dim=1) * scale
            return logits

    with open(input_config.path + ".yml", "r") as f:
        config = yaml.safe_load(f)
    
    with open(input_num_scaler.path, "r") as f:
        num_scaler_dict = json.load(f)
    
    with open(input_encoding_dicts.path, "r") as f:
        encoding_dicts = json.load(f)

    start_listing_time = time.perf_counter()
    storage_client = storage.Client()
    blobs = storage_client.list_blobs(bucket_for_parquet_folder, prefix=gcs_folder_for_train_data)
    files = [
        f'gs://{bucket_for_parquet_folder}/{blob.name}' for blob in blobs if blob.name.endswith('.parquet')]
    print(files, flush=True)
    print(f"Blob listing and file list prep took {time.perf_counter() - start_listing_time:.2f}s", flush=True)

    fs = gcsfs.GCSFileSystem()
    dataset = TrainDataset(files=files, config=config)
    loader = DataLoader(dataset, batch_size=config['model_config']['train_batch_size'])

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = TwoTowerModel(config).to(device)
    lr_value = config['model_config']['lr']
    try:
        lr_value = float(lr_value)
    except (TypeError, ValueError):
        raise ValueError(f"Invalid learning rate in config: {lr_value!r}")
    optimizer = optim.Adam(model.parameters(), lr=lr_value)
    model.train()

    num_epochs = config['model_config']['num_epocks']
    train_loss_history = []
    for epoch in range(num_epochs):
        print(f"=== Epoch {epoch+1} ===")
        running_loss = 0.0
        num_batches = 0
        for step, inputs in enumerate(loader):
            for key, value in inputs.items():
                inputs[key] = value.to(device)
            optimizer.zero_grad()
            logits = model(inputs)
            labels = inputs['label'].to(device)
            weights = inputs['sum_quantity'].to(device)
            loss = F.binary_cross_entropy_with_logits(logits, labels, weight=weights)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()
            num_batches += 1
        
        epoch_loss = running_loss / num_batches if num_batches > 0 else 0.0
        train_loss_history.append(epoch_loss)

    model.eval()
    item_model = model.item_emb
    user_model = model.user_emb
    torch.jit.script(item_model).save(output_item_embedding_model.path + ".pt")
    torch.jit.script(user_model).save(output_user_embedding_model.path + ".pt")

    # with fs.open(gcs_destination_item_embedding_model, 'wb') as f_item:
    #     torch.jit.script(item_model).save(f_item)
    
    # with fs.open(gcs_destination_user_embedding_model, 'wb') as f_user:
    #     torch.jit.script(user_model).save(f_user)

    # ダミー推論
    batch_size = 8
    dims_item = config["model_config"]["item_cat_dims"]
    dims_user = config["model_config"]["user_cat_dims"]
    dummy_item_cat = torch.stack(
        [torch.randint(0, ns, (batch_size,)) for ns, _ in dims_item], dim=1).to(device)
    dummy_item_num = torch.randn(batch_size, len(
        config["model_config"]["item_num_cols"])).to(device)
    dummy_user_cat = torch.stack(
        [torch.randint(0, ns, (batch_size,)) for ns, _ in dims_user], dim=1).to(device)
    dummy_user_num = torch.randn(batch_size, len(
        config["model_config"]["user_num_cols"])).to(device)
    with torch.no_grad():
        out_item = item_model(dummy_item_num, dummy_item_cat)
        out_user = user_model(dummy_user_num, dummy_user_cat)
    import json
    with open(test_output.path, "w") as f:
        json.dump({
            "dummy_item_embedding": out_item.cpu().tolist(),
            "dummy_user_embedding": out_user.cpu().tolist(),
        }, f, indent=2)
