from google.cloud import bigquery
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

if __name__ == "__main__":
    client = bigquery.Client()
    query = """set @@query_label = 'cost:5867';
    SELECT
        dot_product,
        purchased_flag
    FROM
    `labs-science.works_ueno.two_tower_model_sugi_v3_laundry_goods_score_with_purchase_flag`
    """
    df = client.query(query).to_dataframe()

    # 全体の平均購入率
    overall_mean = df['purchased_flag'].mean()

    # Top10～Top100（10%刻み）で累積集計
    cumulative_results = []
    for k in range(10, 101, 10):
        # 上位k%の閾値
        thresh = df['dot_product'].quantile(1 - k/100)
        subset = df[df['dot_product'] >= thresh]
        total = len(subset)
        purchases = subset['purchased_flag'].sum()
        rate = purchases / total if total > 0 else 0
        cumulative_results.append({
            'cumulative_pct': f"Top{k}",
            'total_records': total,
            'purchases': purchases,
            'purchase_rate': rate
        })

    cum_df = pd.DataFrame(cumulative_results)

    # 棒グラフの描画
    plt.figure(figsize=(9,5))
    ax = sns.barplot(
        data=cum_df,
        x='cumulative_pct',
        y='purchase_rate',
        palette='Blues_d'
    )

    # 全体平均のライン
    ax.axhline(
        y=overall_mean,
        color='red',
        linestyle='--',
        linewidth=2,
        label=f'Overall Mean ({overall_mean:.2%})'
    )

    # 各棒の上に購入率を％表示で追記
    for p in ax.patches:
        height = p.get_height()
        ax.annotate(
            f"{height:.1%}",
            xy=(p.get_x() + p.get_width() / 2, height),
            xytext=(0, 5),            # 棒の上に5ポイントだけオフセット
            textcoords="offset points",
            ha="center",
            va="bottom"
        )

    plt.xlabel('Score Cumulative Percentile')
    plt.ylabel('Purchase Rate')
    plt.title('Purchase Rate by Cumulative Top Score Percentile')
    plt.legend()
    plt.tight_layout()
    plt.show()
