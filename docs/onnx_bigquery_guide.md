# Converting Two-Tower Model to ONNX for BigQuery ML

## Overview

This guide explains how to convert your PyTorch two-tower model to ONNX format and use it with BigQuery ML's PREDICT function.

## Recommended Approach: BigQuery Preprocessing + ONNX Model

### Why This Approach?

1. **Simpler ONNX model**: Only contains the neural network logic
2. **Leverages BigQuery's strengths**: Uses built-in ML functions for preprocessing
3. **Better performance**: BigQuery is optimized for large-scale data processing
4. **Easier debugging**: Preprocessing logic is visible in SQL

### Step-by-Step Implementation

#### Step 1: Convert PyTorch Models to ONNX

```bash
# Download your trained models from GCS
gsutil cp gs://works_ueno/embedding_model/v3/user_embedding_model.pt .
gsutil cp gs://works_ueno/embedding_model/v3/item_embedding_model.pt .

# Run the conversion script
python src/convert_to_onnx.py
```

This creates:
- `user_embedding_model.onnx`
- `item_embedding_model.onnx`

#### Step 2: Upload ONNX Models to BigQuery ML

```sql
-- Create user embedding model
CREATE OR REPLACE MODEL `your_project.your_dataset.user_embedding_onnx_model`
OPTIONS(
  model_type='ONNX',
  model_path='gs://your_bucket/models/user_embedding_model.onnx'
);

-- Create item embedding model  
CREATE OR REPLACE MODEL `your_project.your_dataset.item_embedding_onnx_model`
OPTIONS(
  model_type='ONNX',
  model_path='gs://your_bucket/models/item_embedding_model.onnx'
);
```

#### Step 3: Extract Preprocessing Parameters

```bash
# Generate SQL with actual preprocessing parameters
python src/extract_preprocessing_params.py
```

This creates `sql/bigquery_onnx_inference_complete.sql` with your actual normalization means/stds and categorical encodings.

#### Step 4: Use BigQuery ML PREDICT

```sql
-- Use the generated SQL to get embeddings
-- The SQL handles:
-- 1. Numerical feature normalization using training statistics
-- 2. Categorical feature encoding using training mappings  
-- 3. Creating proper input arrays for ONNX models
-- 4. Calling ML.PREDICT to get embeddings

-- Example usage for user embeddings:
SELECT 
  adid,
  user_embedding
FROM (
  -- Your generated preprocessing + prediction SQL
)
```

### Feature Preprocessing Details

#### Numerical Features
Your model uses these user numerical features:
- Demographics: `probability_age_*`, `probability_male`
- Location preferences: 140+ location-based features (LSR_*, SPG_*, etc.)

**Preprocessing**: Standard normalization `(value - mean) / std`

#### Categorical Features
- **User**: `home_pref`, `home_city`, `office_pref`, `office_city`
- **Item**: `upper1_nm`, `upper2_nm`

**Preprocessing**: Label encoding using BigQuery's `ML.LABEL_ENCODER`

### Input/Output Specifications

#### User Model ONNX Inputs:
- `numerical_features`: Float array of shape `[batch_size, 147]` (normalized)
- `categorical_features`: Int64 array of shape `[batch_size, 4]` (encoded)

#### User Model ONNX Output:
- `user_embedding`: Float array of shape `[batch_size, 128]` (L2 normalized)

#### Item Model ONNX Inputs:
- `numerical_features`: Float array of shape `[batch_size, 1]` (avg_price normalized)
- `categorical_features`: Int64 array of shape `[batch_size, 2]` (upper1_nm, upper2_nm encoded)

#### Item Model ONNX Output:
- `item_embedding`: Float array of shape `[batch_size, 128]` (L2 normalized)

## Alternative Approach: Preprocessing in ONNX

### Limitations
- String categorical features are difficult to handle in ONNX
- More complex model structure
- Harder to debug preprocessing issues

### When to Use
- If you need a completely self-contained model
- If you're deploying outside of BigQuery

## Performance Considerations

### BigQuery ML PREDICT Performance
- **Batch processing**: Process multiple users/items at once
- **Partitioning**: Use table partitioning for large datasets
- **Caching**: Consider materializing embeddings for frequently accessed items

### Example Optimized Query Structure
```sql
-- Process in batches and cache results
CREATE OR REPLACE TABLE `your_project.your_dataset.user_embeddings_cache` AS
SELECT 
  adid,
  user_embedding,
  CURRENT_TIMESTAMP() as computed_at
FROM (
  -- Your preprocessing + prediction SQL
)
WHERE adid IN (
  SELECT DISTINCT adid 
  FROM your_active_users_table
);
```

## Troubleshooting

### Common Issues
1. **Feature order mismatch**: Ensure arrays match training order
2. **Missing normalization parameters**: Check that all features have mean/std values
3. **Categorical encoding mismatches**: Verify encoding mappings are complete
4. **ONNX model compatibility**: Use opset_version=11 for BigQuery ML compatibility

### Validation Steps
1. Compare ONNX outputs with PyTorch outputs on same inputs
2. Verify preprocessing matches training pipeline
3. Test with small batch before scaling up

## Next Steps

1. Run the conversion scripts
2. Upload models to BigQuery ML
3. Generate and test the preprocessing SQL
4. Validate outputs against your PyTorch model
5. Scale up to production data

## Files Created
- `src/convert_to_onnx.py`: ONNX conversion script
- `src/extract_preprocessing_params.py`: Parameter extraction script
- `sql/bigquery_onnx_inference.sql`: Template SQL
- `sql/bigquery_onnx_inference_complete.sql`: Generated SQL with actual parameters
