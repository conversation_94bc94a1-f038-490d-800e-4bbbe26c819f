DECLARE item_name STRING;

set @@query_label = 'cost:5867';
set item_name = '洗濯';

-- 変更箇所 2点
-- 1. item_name
-- 2. テーブル名✕2

CREATE TEMP FUNCTION STRING_TO_FLOAT_ARRAY(vec STRING) AS (
  ARRAY(
    SELECT CAST(x AS FLOAT64)
    FROM UNNEST(SPLIT(REGEXP_REPLACE(vec, r'[\[\]\s]', ''), ',')) AS x
    WHERE x <> ''
  )
);

create or replace table `labs-science.works_ueno.two_tower_model_sugi_v3_test_user_laundry_goods_score` as

WITH
  user_vec AS (
    SELECT
      adid,
      STRING_TO_FLOAT_ARRAY(user_embedding) AS u_emb
    FROM `labs-science.works_ueno.two_tower_model_sugi_v3_user_embedding`
  ),
  item_vec AS (
    SELECT
      category,
      STRING_TO_FLOAT_ARRAY(item_embedding) AS i_emb
    FROM `labs-science.works_ueno.two_tower_model_sugi_v3_laundry_goods_embedding`
  ),

  score as (
    SELECT
        u.adid,
        i.category,
    (SELECT SUM(u_val * i_val)
        FROM UNNEST(u.u_emb) AS u_val WITH OFFSET u_idx
        JOIN UNNEST(i.i_emb) AS i_val WITH OFFSET i_idx
        ON u_idx = i_idx)
        AS dot_product
    FROM user_vec AS u
    CROSS JOIN item_vec AS i
    WHERE ARRAY_LENGTH(u.u_emb) = ARRAY_LENGTH(i.i_emb)
    ORDER BY dot_product DESC
  )

SELECT
  s.adid,
  s.category,
  s.dot_product,
  CASE
    WHEN t.adid IS NOT NULL THEN 1
    ELSE 0
  END AS purchased_flag
FROM
  score AS s
LEFT JOIN (
  SELECT DISTINCT adid
  FROM `labs-science.works_ueno.two_tower_model_sugi_v3_test_data`
  WHERE upper2_nm = item_name
) AS t
ON s.adid = t.adid
order by s.dot_product desc




