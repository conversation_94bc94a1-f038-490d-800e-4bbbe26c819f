-- BigQuery SQL for ONNX model inference with preprocessing
-- This SQL preprocesses raw features and feeds them to the ONNX model

-- Step 1: Create the preprocessing logic
WITH feature_preprocessing AS (
  SELECT 
    adid,
    
    -- Numerical feature normalization (replace with actual mean/std from training)
    -- You need to get these values from your normalization_parameters.json
    (probability_age_10s - 0.1234) / 0.5678 AS probability_age_10s_norm,
    (probability_age_20s - 0.2345) / 0.6789 AS probability_age_20s_norm,
    (probability_age_30s - 0.3456) / 0.7890 AS probability_age_30s_norm,
    (probability_age_40s - 0.4567) / 0.8901 AS probability_age_40s_norm,
    (probability_age_50s - 0.5678) / 0.9012 AS probability_age_50s_norm,
    (probability_age_60_70s - 0.6789) / 1.0123 AS probability_age_60_70s_norm,
    (probability_male - 0.7890) / 1.1234 AS probability_male_norm,
    
    -- Add all other numerical features with their normalization parameters
    -- (LSR_SPO_SportsHall - mean_value) / std_value AS LSR_SPO_SportsHall_norm,
    -- ... continue for all numerical features
    
    -- Categorical feature encoding (replace with actual mappings from training)
    -- You need to get these mappings from your cat_encoding_dict.json
    CASE 
      WHEN home_pref = 'Tokyo' THEN 1
      WHEN home_pref = 'Osaka' THEN 2
      WHEN home_pref = 'Kyoto' THEN 3
      -- Add all possible values from your encoding dictionary
      ELSE 0  -- Unknown/padding value
    END AS home_pref_encoded,
    
    CASE 
      WHEN home_city = 'Shibuya' THEN 1
      WHEN home_city = 'Shinjuku' THEN 2
      -- Add all possible values
      ELSE 0
    END AS home_city_encoded,
    
    CASE 
      WHEN office_pref = 'Tokyo' THEN 1
      WHEN office_pref = 'Osaka' THEN 2
      -- Add all possible values
      ELSE 0
    END AS office_pref_encoded,
    
    CASE 
      WHEN office_city = 'Shibuya' THEN 1
      WHEN office_city = 'Shinjuku' THEN 2
      -- Add all possible values
      ELSE 0
    END AS office_city_encoded,
    
    -- Item features (for item embedding)
    (avg_price - avg_price_mean) / avg_price_std AS avg_price_norm,
    
    CASE 
      WHEN upper1_nm = 'Category1' THEN 1
      WHEN upper1_nm = 'Category2' THEN 2
      -- Add all possible values
      ELSE 0
    END AS upper1_nm_encoded,
    
    CASE 
      WHEN upper2_nm = 'SubCategory1' THEN 1
      WHEN upper2_nm = 'SubCategory2' THEN 2
      -- Add all possible values
      ELSE 0
    END AS upper2_nm_encoded
    
  FROM `your_project.your_dataset.raw_user_item_data`
),

-- Step 2: Prepare arrays for ONNX model input
model_inputs AS (
  SELECT 
    adid,
    
    -- User numerical features array (must match training order)
    [
      probability_age_10s_norm,
      probability_age_20s_norm,
      probability_age_30s_norm,
      probability_age_40s_norm,
      probability_age_50s_norm,
      probability_age_60_70s_norm,
      probability_male_norm
      -- Add all other normalized numerical features in the same order as training
    ] AS user_numerical_features,
    
    -- User categorical features array (must match training order)
    [
      home_pref_encoded,
      home_city_encoded,
      office_pref_encoded,
      office_city_encoded
    ] AS user_categorical_features,
    
    -- Item numerical features array
    [avg_price_norm] AS item_numerical_features,
    
    -- Item categorical features array
    [upper1_nm_encoded, upper2_nm_encoded] AS item_categorical_features
    
  FROM feature_preprocessing
)

-- Step 3: Get embeddings using ONNX models
SELECT 
  adid,
  
  -- Get user embedding
  ML.PREDICT(
    MODEL `your_project.your_dataset.user_embedding_onnx_model`,
    (SELECT AS STRUCT 
       user_numerical_features AS numerical_features,
       user_categorical_features AS categorical_features)
  ).user_embedding AS user_embedding,
  
  -- Get item embedding  
  ML.PREDICT(
    MODEL `your_project.your_dataset.item_embedding_onnx_model`,
    (SELECT AS STRUCT 
       item_numerical_features AS numerical_features,
       item_categorical_features AS categorical_features)
  ).item_embedding AS item_embedding

FROM model_inputs;

-- Alternative: If you want to compute similarity scores directly
-- SELECT 
--   adid,
--   -- Compute dot product similarity between user and item embeddings
--   (
--     SELECT SUM(u * i) 
--     FROM UNNEST(user_embedding) AS u WITH OFFSET pos1
--     JOIN UNNEST(item_embedding) AS i WITH OFFSET pos2 
--     ON pos1 = pos2
--   ) AS similarity_score
-- FROM (
--   -- ... previous query to get embeddings
-- )
