DECLARE start_date STRING;
DECLARE end_date STRING;

set start_date = '********';
set end_date = '********';
set @@query_label = 'cost:5867';

-- スギの生の購買データからデータの抽出（カラムの取捨選択と行の絞り込み）
CREATE OR REPLACE TABLE `labs-science.works_ueno.sugi_v3_user_item_data_filtered_adid_in_bb` as

WITH t_transaction AS (
  SELECT DISTINCT
    id_mbr,
    ch_sex as gender,
    jancode,
    quantity,
    unit_amt,
    biz_day_line AS sales_date,
    trade_time
  FROM
    `beaconbank-datawarehouse.sugi2unerry.sales_item_*`
  WHERE
    _TABLE_SUFFIX BETWEEN start_date AND end_date
),

-- スギが持ってるadid
adid_map AS (
  SELECT DISTINCT
    id_mbr AS member_id,
    adid
  FROM
    `beaconbank-datawarehouse.sugi2unerry.fb_app_agreed_users_*`
  WHERE
    _TABLE_SUFFIX BETWEEN start_date AND end_date
    AND adid IS NOT NULL
    AND adid <> ''
    AND adid <> '********-0000-0000-0000-************'
    AND adid <> '0000-0000'
),

-- JANマスタ
jan_map AS (
  SELECT DISTINCT
    cd_jan AS jan_code,
    nm_pdt_knj,
    upper1_nm,
    upper2_nm,
    upper3_nm,
  FROM
    `beaconbank-datawarehouse.sugi2unerry.m_jan_*`
  WHERE
    _TABLE_SUFFIX BETWEEN start_date AND end_date
),

user_item AS (
  SELECT DISTINCT
    t1.sales_date,
    t1.trade_time,
    t2.adid,
    t1.gender as real_gender,
    t4.* except(user_id, Total),
    t5.probability_age_10s,
    t5.probability_age_20s,
    t5.probability_age_30s,
    t5.probability_age_40s,
    t5.probability_age_50s,
    t5.probability_age_60_70s,
    t6.probability_male,
    t7.prefecture as home_pref,
    t7.city as home_city,
    t8.prefecture as office_pref,
    t8.city as office_city,
    t1.jancode,
    t3.nm_pdt_knj,
    t3.upper1_nm,
    t3.upper2_nm,
    t3.upper3_nm,
    t1.quantity,
    t1.unit_amt,
  FROM
    t_transaction AS t1
  JOIN
    adid_map AS t2
    ON t1.id_mbr = t2.member_id
  JOIN
    jan_map AS t3
    ON t1.jancode = t3.jan_code
  JOIN
    `labs-science.works_ueno.poi_count_2024` t4 ON t2.adid = t4.user_id
  JOIN
    `kassai-dwh.jp_dwh_latest.dim_user_profile_age_rev1` t5 ON t2.adid = t5.user_id
  JOIN
    `kassai-dwh.jp_dwh_latest.dim_user_profile_gender_rev1` t6 ON t2.adid = t6.user_id
  JOIN
    `beaconbank-analytics.user_profile_v2.vw_home_h3_estat_4m` t7 ON t2.adid = t7.id
  JOIN
    `beaconbank-analytics.user_profile_v2.vw_home_h3_estat_4m` t8 ON t2.adid = t8.id
  WHERE t3.upper2_nm IN (
    'フェイスケア',
    'ヘアケア',
    'ボディケア',
    'メイク',
    'メンズケア',
    'ドライ食品',
    'パン',
    '飲料',
    '菓子',
    '酒',
    '日配食品',
    'オーラルケア',
    '健康食品',
    'スマイルケア食',
    'ベビーフード',
    'ベビー用品',
    '住居',
    '洗濯',
    '台所'
  )
  ORDER BY
    t3.upper1_nm,
    t3.upper2_nm,
    t3.upper3_nm,
    t3.nm_pdt_knj,
    t2.adid,
    t1.gender,
    t1.sales_date
),

item_avg_price AS (
  select distinct
    upper1_nm,
    upper2_nm,
    CAST(AVG(unit_amt) AS FLOAT64) AS avg_price
  from
    user_item
  group by upper1_nm, upper2_nm
)

SELECT
  t1.* except(unit_amt),
  t2.avg_price
FROM
  user_item t1
  inner join item_avg_price t2 on t1.upper1_nm = t2.upper1_nm and t1.upper2_nm = t2.upper2_nm
ORDER BY
  t1.sales_date




