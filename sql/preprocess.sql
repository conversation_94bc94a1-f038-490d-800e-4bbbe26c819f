set @@query_label = 'cost:5867';

create or replace table `labs-science.works_ueno.two_tower_model_sugi_v3_train_data_preprocessed` as

with handle_missing as (
  select
    sales_date,
    adid,
    IFNULL(LSR_SPO_SportsHall, 0) AS LSR_SPO_SportsHall,
    IFNULL(LSR_SPO_FitnessGym, 0) AS LSR_SPO_FitnessGym,
    IFNULL(LSR_SPO_OtherSports, 0) AS LSR_SPO_OtherSports,
    IFNULL(LSR_OTD_ParkGarden, 0) AS LSR_OTD_ParkGarden,
    IFNULL(LSR_OTD_BeachRiver, 0) AS LSR_OTD_BeachRiver,
    IFNULL(SPG_SPC_BookStore, 0) AS SPG_SPC_BookStore,
    IFNULL(SPG_SPC_RentVideoCD, 0) AS SPG_SPC_RentVideoCD,
    IFNULL(SPG_SPC_HousingExpo, 0) AS SPG_SPC_HousingExpo,
    IFNULL(SPG_SPC_OutdoorGoodsStore, 0) AS SPG_SPC_OutdoorGoodsStore,
    IFNULL(SPG_SPC_LiquorStore, 0) AS SPG_SPC_LiquorStore,
    IFNULL(LSR_AMS_Arcade, 0) AS LSR_AMS_Arcade,
    IFNULL(SPG_SPC_PreOwnedCarDealer, 0) AS SPG_SPC_PreOwnedCarDealer,
    IFNULL(SPG_SPC_BabyKids, 0) AS SPG_SPC_BabyKids,
    IFNULL(LSR_AMS_Karaoke, 0) AS LSR_AMS_Karaoke,
    IFNULL(SPG_SPC_OtherSpecialityStore, 0) AS SPG_SPC_OtherSpecialityStore,
    IFNULL(LFS_CAR_Massage, 0) AS LFS_CAR_Massage,
    IFNULL(LFS_CAR_Aesthetic, 0) AS LFS_CAR_Aesthetic,
    IFNULL(SPG_FSH_Lingerie, 0) AS SPG_FSH_Lingerie,
    IFNULL(LFS_CAR_Spa, 0) AS LFS_CAR_Spa,
    IFNULL(LFS_CAR_PetService, 0) AS LFS_CAR_PetService,
    IFNULL(SPG_FSH_SecondHand, 0) AS SPG_FSH_SecondHand,
    IFNULL(GRM_SPC_Chinese, 0) AS GRM_SPC_Chinese,
    IFNULL(GRM_SPC_Italian, 0) AS GRM_SPC_Italian,
    IFNULL(GRM_SPC_NigiriSushi, 0) AS GRM_SPC_NigiriSushi,
    IFNULL(GRM_SPC_Steak, 0) AS GRM_SPC_Steak,
    IFNULL(GRM_SPC_OtherRestaurant, 0) AS GRM_SPC_OtherRestaurant,
    IFNULL(SPG_LRG_HomeApplianceStore, 0) AS SPG_LRG_HomeApplianceStore,
    IFNULL(LFS_PUB_Police, 0) AS LFS_PUB_Police,
    IFNULL(SPG_LRG_HomeCenter, 0) AS SPG_LRG_HomeCenter,
    IFNULL(LFS_PUB_GovernmentOffice, 0) AS LFS_PUB_GovernmentOffice,
    IFNULL(LFS_PUB_Courtyard, 0) AS LFS_PUB_Courtyard,
    IFNULL(SPG_LRG_FurnitureStore, 0) AS SPG_LRG_FurnitureStore,
    IFNULL(SPG_LRG_OtherSC, 0) AS SPG_LRG_OtherSC,
    IFNULL(GRM_CFS_Cafe, 0) AS GRM_CFS_Cafe,
    IFNULL(SPG_SML_MiniMarket, 0) AS SPG_SML_MiniMarket,
    IFNULL(LFS_HSP_Clinics, 0) AS LFS_HSP_Clinics,
    IFNULL(SPG_SML_ConvenienceStore, 0) AS SPG_SML_ConvenienceStore,
    IFNULL(SPG_SML_OneDollerMarket, 0) AS SPG_SML_OneDollerMarket,
    IFNULL(GRM_FMR_Japanese, 0) AS GRM_FMR_Japanese,
    IFNULL(GRM_FMR_Western, 0) AS GRM_FMR_Western,
    IFNULL(GRM_FMR_Chinese, 0) AS GRM_FMR_Chinese,
    IFNULL(GRM_FMR_Variety, 0) AS GRM_FMR_Variety,
    IFNULL(LSR_OTR_OtherEntertainment, 0) AS LSR_OTR_OtherEntertainment,
    IFNULL(LFS_SCL_LanguageSchool, 0) AS LFS_SCL_LanguageSchool,
    IFNULL(LFS_SCL_ArtCraft, 0) AS LFS_SCL_ArtCraft,
    IFNULL(LFS_SCL_TraditionalCulture, 0) AS LFS_SCL_TraditionalCulture,
    IFNULL(LFS_SCL_Dance, 0) AS LFS_SCL_Dance,
    IFNULL(LFS_SCL_Music, 0) AS LFS_SCL_Music,
    IFNULL(LFS_SCL_Cooking, 0) AS LFS_SCL_Cooking,
    IFNULL(GRM_FST_HamburgerShop, 0) AS GRM_FST_HamburgerShop,
    IFNULL(GRM_FST_UdonSoba, 0) AS GRM_FST_UdonSoba,
    IFNULL(GRM_FST_Takeout, 0) AS GRM_FST_Takeout,
    IFNULL(LSR_ACM_Hotel, 0) AS LSR_ACM_Hotel,
    IFNULL(LSR_ACM_JapaneseHotel, 0) AS LSR_ACM_JapaneseHotel,
    IFNULL(LFS_EDU_HighSchool, 0) AS LFS_EDU_HighSchool,
    IFNULL(LSR_ACM_BnB, 0) AS LSR_ACM_BnB,
    IFNULL(LFS_EDU_ElementarySchool, 0) AS LFS_EDU_ElementarySchool,
    IFNULL(LSR_ACM_LeisureHotel, 0) AS LSR_ACM_LeisureHotel,
    IFNULL(LFS_EDU_VocationalSchool, 0) AS LFS_EDU_VocationalSchool,
    IFNULL(LFS_EDU_PrepSchool, 0) AS LFS_EDU_PrepSchool,
    IFNULL(LFS_FIN_Bank, 0) AS LFS_FIN_Bank,
    IFNULL(LSR_APR_ArtMuseum, 0) AS LSR_APR_ArtMuseum,
    IFNULL(LSR_APR_Archive, 0) AS LSR_APR_Archive,
    IFNULL(LSR_APR_Aquarium, 0) AS LSR_APR_Aquarium,
    IFNULL(LSR_APR_LiveHouse, 0) AS LSR_APR_LiveHouse,
    IFNULL(LSR_APR_OtherAppreciation, 0) AS LSR_APR_OtherAppreciation,
    IFNULL(LFS_TRS_FerryTerminal, 0) AS LFS_TRS_FerryTerminal,
    IFNULL(LFS_TRS_SAPA, 0) AS LFS_TRS_SAPA,
    IFNULL(LFS_TRS_CarCareService, 0) AS LFS_TRS_CarCareService,
    IFNULL(LFS_TRS_Station, 0) AS LFS_TRS_Station,
    IFNULL(LFS_TRS_BusTerminal, 0) AS LFS_TRS_BusTerminal,
    IFNULL(LSR_SPO_Ballpark, 0) AS LSR_SPO_Ballpark,
    IFNULL(LSR_SPO_Budo, 0) AS LSR_SPO_Budo,
    IFNULL(LSR_OTD_ShrineTemple, 0) AS LSR_OTD_ShrineTemple,
    IFNULL(LSR_OTD_Camp, 0) AS LSR_OTD_Camp,
    IFNULL(SPG_SPC_CarSupplyStore, 0) AS SPG_SPC_CarSupplyStore,
    IFNULL(SPG_SPC_SportsGoodsStore, 0) AS SPG_SPC_SportsGoodsStore,
    IFNULL(SPG_SPC_MobileStore, 0) AS SPG_SPC_MobileStore,
    IFNULL(LSR_AMS_Themepark, 0) AS LSR_AMS_Themepark,
    IFNULL(SPG_SPC_CarDealer, 0) AS SPG_SPC_CarDealer,
    IFNULL(LSR_AMS_InternetComicCafe, 0) AS LSR_AMS_InternetComicCafe,
    IFNULL(SPG_FSH_Apparel, 0) AS SPG_FSH_Apparel,
    IFNULL(LFS_CAR_Hair, 0) AS LFS_CAR_Hair,
    IFNULL(SPG_FSH_Kimono, 0) AS SPG_FSH_Kimono,
    IFNULL(SPG_FSH_FormalWear, 0) AS SPG_FSH_FormalWear,
    IFNULL(SPG_FSH_ShoeStore, 0) AS SPG_FSH_ShoeStore,
    IFNULL(LFS_CAR_Laundry, 0) AS LFS_CAR_Laundry,
    IFNULL(SPG_FSH_Bag, 0) AS SPG_FSH_Bag,
    IFNULL(SPG_FSH_Accessory, 0) AS SPG_FSH_Accessory,
    IFNULL(GRM_SPC_AsianEthnic, 0) AS GRM_SPC_AsianEthnic,
    IFNULL(GRM_SPC_French, 0) AS GRM_SPC_French,
    IFNULL(GRM_SPC_Izakaya, 0) AS GRM_SPC_Izakaya,
    IFNULL(GRM_SPC_BarPubClub, 0) AS GRM_SPC_BarPubClub,
    IFNULL(GRM_SPC_TraditionalJapanese, 0) AS GRM_SPC_TraditionalJapanese,
    IFNULL(GRM_SPC_Unagi, 0) AS GRM_SPC_Unagi,
    IFNULL(GRM_SPC_Yakiniku, 0) AS GRM_SPC_Yakiniku,
    IFNULL(SPG_LRG_DepartmentStore, 0) AS SPG_LRG_DepartmentStore,
    IFNULL(LFS_PUB_MunicipalOffice, 0) AS LFS_PUB_MunicipalOffice,
    IFNULL(LFS_PUB_FireFighting, 0) AS LFS_PUB_FireFighting,
    IFNULL(SPG_LRG_ShoppingMallComplex, 0) AS SPG_LRG_ShoppingMallComplex,
    IFNULL(LFS_PUB_FuneralWedding, 0) AS LFS_PUB_FuneralWedding,
    IFNULL(SPG_LRG_OutletMall, 0) AS SPG_LRG_OutletMall,
    IFNULL(LFS_PUB_Embassies, 0) AS LFS_PUB_Embassies,
    IFNULL(SPG_LRG_RecycleShop, 0) AS SPG_LRG_RecycleShop,
    IFNULL(SPG_LRG_MiscellaneousGoodsStore, 0) AS SPG_LRG_MiscellaneousGoodsStore,
    IFNULL(LFS_PUB_Library, 0) AS LFS_PUB_Library,
    IFNULL(GRM_CFS_JapaneseSweets, 0) AS GRM_CFS_JapaneseSweets,
    IFNULL(GRM_CFS_WesternSweets, 0) AS GRM_CFS_WesternSweets,
    IFNULL(GRM_CFS_OtherCafeSweets, 0) AS GRM_CFS_OtherCafeSweets,
    IFNULL(SPG_SML_FoodMarket, 0) AS SPG_SML_FoodMarket,
    IFNULL(LFS_HSP_GeneralHospitals, 0) AS LFS_HSP_GeneralHospitals,
    IFNULL(SPG_SML_DrugStore, 0) AS SPG_SML_DrugStore,
    IFNULL(SPG_SML_DiscountStore, 0) AS SPG_SML_DiscountStore,
    IFNULL(GRM_FMR_Yakiniku, 0) AS GRM_FMR_Yakiniku,
    IFNULL(GRM_FMR_Sushi, 0) AS GRM_FMR_Sushi,
    IFNULL(LFS_SCL_BusinessSchool, 0) AS LFS_SCL_BusinessSchool,
    IFNULL(LFS_SCL_Computer, 0) AS LFS_SCL_Computer,
    IFNULL(GRM_FST_DonTeishoku, 0) AS GRM_FST_DonTeishoku,
    IFNULL(GRM_FST_Ramen, 0) AS GRM_FST_Ramen,
    IFNULL(GRM_FST_Bakery, 0) AS GRM_FST_Bakery,
    IFNULL(GRM_FST_Curry, 0) AS GRM_FST_Curry,
    IFNULL(GRM_FST_OtherFastFood, 0) AS GRM_FST_OtherFastFood,
    IFNULL(LFS_EDU_University, 0) AS LFS_EDU_University,
    IFNULL(LSR_ACM_BusinessHotel, 0) AS LSR_ACM_BusinessHotel,
    IFNULL(LFS_EDU_JuniorHighSchool, 0) AS LFS_EDU_JuniorHighSchool,
    IFNULL(LFS_EDU_KindergartenNursery, 0) AS LFS_EDU_KindergartenNursery,
    IFNULL(LSR_ACM_OtherAccomodation, 0) AS LSR_ACM_OtherAccomodation,
    IFNULL(LSR_APR_Theater, 0) AS LSR_APR_Theater,
    IFNULL(LSR_APR_Hall, 0) AS LSR_APR_Hall,
    IFNULL(LFS_FIN_PostOffice, 0) AS LFS_FIN_PostOffice,
    IFNULL(LSR_APR_Museum, 0) AS LSR_APR_Museum,
    IFNULL(LSR_APR_ZooBotanical, 0) AS LSR_APR_ZooBotanical,
    IFNULL(LFS_TRS_Parking, 0) AS LFS_TRS_Parking,
    IFNULL(LFS_TRS_GasStation, 0) AS LFS_TRS_GasStation,
    IFNULL(LFS_TRS_RoadsideRest, 0) AS LFS_TRS_RoadsideRest,
    IFNULL(LFS_TRS_Airport, 0) AS LFS_TRS_Airport,
    IFNULL(LFS_TRS_CarRental, 0) AS LFS_TRS_CarRental,
    IFNULL(LFS_TRS_DepartmentOfMotorVehicles, 0) AS LFS_TRS_DepartmentOfMotorVehicles,
    IFNULL(LSR_SPO_Stadium, 0) AS LSR_SPO_Stadium,
    IFNULL(LSR_SPO_SnowMountain, 0) AS LSR_SPO_SnowMountain,
    IFNULL(LSR_SPO_Tennis, 0) AS LSR_SPO_Tennis,
    IFNULL(LSR_SPO_Golf, 0) AS LSR_SPO_Golf,
    IFNULL(LSR_SPO_Pool, 0) AS LSR_SPO_Pool,
    probability_age_10s,
    probability_age_20s,
    probability_age_30s,
    probability_age_40s,
    probability_age_50s,
    probability_age_60_70s,
    probability_male,
    home_pref,
    home_city,
    office_pref,
    office_city,
    upper1_nm,
    upper2_nm,
    quantity,
    avg_price
  from
    `labs-science.works_ueno.two_tower_model_sugi_v3_train_data`
),

standardize as (
  select
    sales_date,
    adid,
    ML.STANDARD_SCALER(LSR_SPO_SportsHall) OVER() AS LSR_SPO_SportsHall,
    ML.STANDARD_SCALER(LSR_SPO_FitnessGym) OVER() AS LSR_SPO_FitnessGym,
    ML.STANDARD_SCALER(LSR_SPO_OtherSports) OVER() AS LSR_SPO_OtherSports,
    ML.STANDARD_SCALER(LSR_OTD_ParkGarden) OVER() AS LSR_OTD_ParkGarden,
    ML.STANDARD_SCALER(LSR_OTD_BeachRiver) OVER() AS LSR_OTD_BeachRiver,
    ML.STANDARD_SCALER(SPG_SPC_BookStore) OVER() AS SPG_SPC_BookStore,
    ML.STANDARD_SCALER(SPG_SPC_RentVideoCD) OVER() AS SPG_SPC_RentVideoCD,
    ML.STANDARD_SCALER(SPG_SPC_HousingExpo) OVER() AS SPG_SPC_HousingExpo,
    ML.STANDARD_SCALER(SPG_SPC_OutdoorGoodsStore) OVER() AS SPG_SPC_OutdoorGoodsStore,
    ML.STANDARD_SCALER(SPG_SPC_LiquorStore) OVER() AS SPG_SPC_LiquorStore,
    ML.STANDARD_SCALER(LSR_AMS_Arcade) OVER() AS LSR_AMS_Arcade,
    ML.STANDARD_SCALER(SPG_SPC_PreOwnedCarDealer) OVER() AS SPG_SPC_PreOwnedCarDealer,
    ML.STANDARD_SCALER(SPG_SPC_BabyKids) OVER() AS SPG_SPC_BabyKids,
    ML.STANDARD_SCALER(LSR_AMS_Karaoke) OVER() AS LSR_AMS_Karaoke,
    ML.STANDARD_SCALER(SPG_SPC_OtherSpecialityStore) OVER() AS SPG_SPC_OtherSpecialityStore,
    ML.STANDARD_SCALER(LFS_CAR_Massage) OVER() AS LFS_CAR_Massage,
    ML.STANDARD_SCALER(LFS_CAR_Aesthetic) OVER() AS LFS_CAR_Aesthetic,
    ML.STANDARD_SCALER(SPG_FSH_Lingerie) OVER() AS SPG_FSH_Lingerie,
    ML.STANDARD_SCALER(LFS_CAR_Spa) OVER() AS LFS_CAR_Spa,
    ML.STANDARD_SCALER(LFS_CAR_PetService) OVER() AS LFS_CAR_PetService,
    ML.STANDARD_SCALER(SPG_FSH_SecondHand) OVER() AS SPG_FSH_SecondHand,
    ML.STANDARD_SCALER(GRM_SPC_Chinese) OVER() AS GRM_SPC_Chinese,
    ML.STANDARD_SCALER(GRM_SPC_Italian) OVER() AS GRM_SPC_Italian,
    ML.STANDARD_SCALER(GRM_SPC_NigiriSushi) OVER() AS GRM_SPC_NigiriSushi,
    ML.STANDARD_SCALER(GRM_SPC_Steak) OVER() AS GRM_SPC_Steak,
    ML.STANDARD_SCALER(GRM_SPC_OtherRestaurant) OVER() AS GRM_SPC_OtherRestaurant,
    ML.STANDARD_SCALER(SPG_LRG_HomeApplianceStore) OVER() AS SPG_LRG_HomeApplianceStore,
    ML.STANDARD_SCALER(LFS_PUB_Police) OVER() AS LFS_PUB_Police,
    ML.STANDARD_SCALER(SPG_LRG_HomeCenter) OVER() AS SPG_LRG_HomeCenter,
    ML.STANDARD_SCALER(LFS_PUB_GovernmentOffice) OVER() AS LFS_PUB_GovernmentOffice,
    ML.STANDARD_SCALER(LFS_PUB_Courtyard) OVER() AS LFS_PUB_Courtyard,
    ML.STANDARD_SCALER(SPG_LRG_FurnitureStore) OVER() AS SPG_LRG_FurnitureStore,
    ML.STANDARD_SCALER(SPG_LRG_OtherSC) OVER() AS SPG_LRG_OtherSC,
    ML.STANDARD_SCALER(GRM_CFS_Cafe) OVER() AS GRM_CFS_Cafe,
    ML.STANDARD_SCALER(SPG_SML_MiniMarket) OVER() AS SPG_SML_MiniMarket,
    ML.STANDARD_SCALER(LFS_HSP_Clinics) OVER() AS LFS_HSP_Clinics,
    ML.STANDARD_SCALER(SPG_SML_ConvenienceStore) OVER() AS SPG_SML_ConvenienceStore,
    ML.STANDARD_SCALER(SPG_SML_OneDollerMarket) OVER() AS SPG_SML_OneDollerMarket,
    ML.STANDARD_SCALER(GRM_FMR_Japanese) OVER() AS GRM_FMR_Japanese,
    ML.STANDARD_SCALER(GRM_FMR_Western) OVER() AS GRM_FMR_Western,
    ML.STANDARD_SCALER(GRM_FMR_Chinese) OVER() AS GRM_FMR_Chinese,
    ML.STANDARD_SCALER(GRM_FMR_Variety) OVER() AS GRM_FMR_Variety,
    ML.STANDARD_SCALER(LSR_OTR_OtherEntertainment) OVER() AS LSR_OTR_OtherEntertainment,
    ML.STANDARD_SCALER(LFS_SCL_LanguageSchool) OVER() AS LFS_SCL_LanguageSchool,
    ML.STANDARD_SCALER(LFS_SCL_ArtCraft) OVER() AS LFS_SCL_ArtCraft,
    ML.STANDARD_SCALER(LFS_SCL_TraditionalCulture) OVER() AS LFS_SCL_TraditionalCulture,
    ML.STANDARD_SCALER(LFS_SCL_Dance) OVER() AS LFS_SCL_Dance,
    ML.STANDARD_SCALER(LFS_SCL_Music) OVER() AS LFS_SCL_Music,
    ML.STANDARD_SCALER(LFS_SCL_Cooking) OVER() AS LFS_SCL_Cooking,
    ML.STANDARD_SCALER(GRM_FST_HamburgerShop) OVER() AS GRM_FST_HamburgerShop,
    ML.STANDARD_SCALER(GRM_FST_UdonSoba) OVER() AS GRM_FST_UdonSoba,
    ML.STANDARD_SCALER(GRM_FST_Takeout) OVER() AS GRM_FST_Takeout,
    ML.STANDARD_SCALER(LSR_ACM_Hotel) OVER() AS LSR_ACM_Hotel,
    ML.STANDARD_SCALER(LSR_ACM_JapaneseHotel) OVER() AS LSR_ACM_JapaneseHotel,
    ML.STANDARD_SCALER(LFS_EDU_HighSchool) OVER() AS LFS_EDU_HighSchool,
    ML.STANDARD_SCALER(LSR_ACM_BnB) OVER() AS LSR_ACM_BnB,
    ML.STANDARD_SCALER(LFS_EDU_ElementarySchool) OVER() AS LFS_EDU_ElementarySchool,
    ML.STANDARD_SCALER(LSR_ACM_LeisureHotel) OVER() AS LSR_ACM_LeisureHotel,
    ML.STANDARD_SCALER(LFS_EDU_VocationalSchool) OVER() AS LFS_EDU_VocationalSchool,
    ML.STANDARD_SCALER(LFS_EDU_PrepSchool) OVER() AS LFS_EDU_PrepSchool,
    ML.STANDARD_SCALER(LFS_FIN_Bank) OVER() AS LFS_FIN_Bank,
    ML.STANDARD_SCALER(LSR_APR_ArtMuseum) OVER() AS LSR_APR_ArtMuseum,
    ML.STANDARD_SCALER(LSR_APR_Archive) OVER() AS LSR_APR_Archive,
    ML.STANDARD_SCALER(LSR_APR_Aquarium) OVER() AS LSR_APR_Aquarium,
    ML.STANDARD_SCALER(LSR_APR_LiveHouse) OVER() AS LSR_APR_LiveHouse,
    ML.STANDARD_SCALER(LSR_APR_OtherAppreciation) OVER() AS LSR_APR_OtherAppreciation,
    ML.STANDARD_SCALER(LFS_TRS_FerryTerminal) OVER() AS LFS_TRS_FerryTerminal,
    ML.STANDARD_SCALER(LFS_TRS_SAPA) OVER() AS LFS_TRS_SAPA,
    ML.STANDARD_SCALER(LFS_TRS_CarCareService) OVER() AS LFS_TRS_CarCareService,
    ML.STANDARD_SCALER(LFS_TRS_Station) OVER() AS LFS_TRS_Station,
    ML.STANDARD_SCALER(LFS_TRS_BusTerminal) OVER() AS LFS_TRS_BusTerminal,
    ML.STANDARD_SCALER(LSR_SPO_Ballpark) OVER() AS LSR_SPO_Ballpark,
    ML.STANDARD_SCALER(LSR_SPO_Budo) OVER() AS LSR_SPO_Budo,
    ML.STANDARD_SCALER(LSR_OTD_ShrineTemple) OVER() AS LSR_OTD_ShrineTemple,
    ML.STANDARD_SCALER(LSR_OTD_Camp) OVER() AS LSR_OTD_Camp,
    ML.STANDARD_SCALER(SPG_SPC_CarSupplyStore) OVER() AS SPG_SPC_CarSupplyStore,
    ML.STANDARD_SCALER(SPG_SPC_SportsGoodsStore) OVER() AS SPG_SPC_SportsGoodsStore,
    ML.STANDARD_SCALER(SPG_SPC_MobileStore) OVER() AS SPG_SPC_MobileStore,
    ML.STANDARD_SCALER(LSR_AMS_Themepark) OVER() AS LSR_AMS_Themepark,
    ML.STANDARD_SCALER(SPG_SPC_CarDealer) OVER() AS SPG_SPC_CarDealer,
    ML.STANDARD_SCALER(LSR_AMS_InternetComicCafe) OVER() AS LSR_AMS_InternetComicCafe,
    ML.STANDARD_SCALER(SPG_FSH_Apparel) OVER() AS SPG_FSH_Apparel,
    ML.STANDARD_SCALER(LFS_CAR_Hair) OVER() AS LFS_CAR_Hair,
    ML.STANDARD_SCALER(SPG_FSH_Kimono) OVER() AS SPG_FSH_Kimono,
    ML.STANDARD_SCALER(SPG_FSH_FormalWear) OVER() AS SPG_FSH_FormalWear,
    ML.STANDARD_SCALER(SPG_FSH_ShoeStore) OVER() AS SPG_FSH_ShoeStore,
    ML.STANDARD_SCALER(LFS_CAR_Laundry) OVER() AS LFS_CAR_Laundry,
    ML.STANDARD_SCALER(SPG_FSH_Bag) OVER() AS SPG_FSH_Bag,
    ML.STANDARD_SCALER(SPG_FSH_Accessory) OVER() AS SPG_FSH_Accessory,
    ML.STANDARD_SCALER(GRM_SPC_AsianEthnic) OVER() AS GRM_SPC_AsianEthnic,
    ML.STANDARD_SCALER(GRM_SPC_French) OVER() AS GRM_SPC_French,
    ML.STANDARD_SCALER(GRM_SPC_Izakaya) OVER() AS GRM_SPC_Izakaya,
    ML.STANDARD_SCALER(GRM_SPC_BarPubClub) OVER() AS GRM_SPC_BarPubClub,
    ML.STANDARD_SCALER(GRM_SPC_TraditionalJapanese) OVER() AS GRM_SPC_TraditionalJapanese,
    ML.STANDARD_SCALER(GRM_SPC_Unagi) OVER() AS GRM_SPC_Unagi,
    ML.STANDARD_SCALER(GRM_SPC_Yakiniku) OVER() AS GRM_SPC_Yakiniku,
    ML.STANDARD_SCALER(SPG_LRG_DepartmentStore) OVER() AS SPG_LRG_DepartmentStore,
    ML.STANDARD_SCALER(LFS_PUB_MunicipalOffice) OVER() AS LFS_PUB_MunicipalOffice,
    ML.STANDARD_SCALER(LFS_PUB_FireFighting) OVER() AS LFS_PUB_FireFighting,
    ML.STANDARD_SCALER(SPG_LRG_ShoppingMallComplex) OVER() AS SPG_LRG_ShoppingMallComplex,
    ML.STANDARD_SCALER(LFS_PUB_FuneralWedding) OVER() AS LFS_PUB_FuneralWedding,
    ML.STANDARD_SCALER(SPG_LRG_OutletMall) OVER() AS SPG_LRG_OutletMall,
    ML.STANDARD_SCALER(LFS_PUB_Embassies) OVER() AS LFS_PUB_Embassies,
    ML.STANDARD_SCALER(SPG_LRG_RecycleShop) OVER() AS SPG_LRG_RecycleShop,
    ML.STANDARD_SCALER(SPG_LRG_MiscellaneousGoodsStore) OVER() AS SPG_LRG_MiscellaneousGoodsStore,
    ML.STANDARD_SCALER(LFS_PUB_Library) OVER() AS LFS_PUB_Library,
    ML.STANDARD_SCALER(GRM_CFS_JapaneseSweets) OVER() AS GRM_CFS_JapaneseSweets,
    ML.STANDARD_SCALER(GRM_CFS_WesternSweets) OVER() AS GRM_CFS_WesternSweets,
    ML.STANDARD_SCALER(GRM_CFS_OtherCafeSweets) OVER() AS GRM_CFS_OtherCafeSweets,
    ML.STANDARD_SCALER(SPG_SML_FoodMarket) OVER() AS SPG_SML_FoodMarket,
    ML.STANDARD_SCALER(LFS_HSP_GeneralHospitals) OVER() AS LFS_HSP_GeneralHospitals,
    ML.STANDARD_SCALER(SPG_SML_DrugStore) OVER() AS SPG_SML_DrugStore,
    ML.STANDARD_SCALER(SPG_SML_DiscountStore) OVER() AS SPG_SML_DiscountStore,
    ML.STANDARD_SCALER(GRM_FMR_Yakiniku) OVER() AS GRM_FMR_Yakiniku,
    ML.STANDARD_SCALER(GRM_FMR_Sushi) OVER() AS GRM_FMR_Sushi,
    ML.STANDARD_SCALER(LFS_SCL_BusinessSchool) OVER() AS LFS_SCL_BusinessSchool,
    ML.STANDARD_SCALER(LFS_SCL_Computer) OVER() AS LFS_SCL_Computer,
    ML.STANDARD_SCALER(GRM_FST_DonTeishoku) OVER() AS GRM_FST_DonTeishoku,
    ML.STANDARD_SCALER(GRM_FST_Ramen) OVER() AS GRM_FST_Ramen,
    ML.STANDARD_SCALER(GRM_FST_Bakery) OVER() AS GRM_FST_Bakery,
    ML.STANDARD_SCALER(GRM_FST_Curry) OVER() AS GRM_FST_Curry,
    ML.STANDARD_SCALER(GRM_FST_OtherFastFood) OVER() AS GRM_FST_OtherFastFood,
    ML.STANDARD_SCALER(LFS_EDU_University) OVER() AS LFS_EDU_University,
    ML.STANDARD_SCALER(LSR_ACM_BusinessHotel) OVER() AS LSR_ACM_BusinessHotel,
    ML.STANDARD_SCALER(LFS_EDU_JuniorHighSchool) OVER() AS LFS_EDU_JuniorHighSchool,
    ML.STANDARD_SCALER(LFS_EDU_KindergartenNursery) OVER() AS LFS_EDU_KindergartenNursery,
    ML.STANDARD_SCALER(LSR_ACM_OtherAccomodation) OVER() AS LSR_ACM_OtherAccomodation,
    ML.STANDARD_SCALER(LSR_APR_Theater) OVER() AS LSR_APR_Theater,
    ML.STANDARD_SCALER(LSR_APR_Hall) OVER() AS LSR_APR_Hall,
    ML.STANDARD_SCALER(LFS_FIN_PostOffice) OVER() AS LFS_FIN_PostOffice,
    ML.STANDARD_SCALER(LSR_APR_Museum) OVER() AS LSR_APR_Museum,
    ML.STANDARD_SCALER(LSR_APR_ZooBotanical) OVER() AS LSR_APR_ZooBotanical,
    ML.STANDARD_SCALER(LFS_TRS_Parking) OVER() AS LFS_TRS_Parking,
    ML.STANDARD_SCALER(LFS_TRS_GasStation) OVER() AS LFS_TRS_GasStation,
    ML.STANDARD_SCALER(LFS_TRS_RoadsideRest) OVER() AS LFS_TRS_RoadsideRest,
    ML.STANDARD_SCALER(LFS_TRS_Airport) OVER() AS LFS_TRS_Airport,
    ML.STANDARD_SCALER(LFS_TRS_CarRental) OVER() AS LFS_TRS_CarRental,
    ML.STANDARD_SCALER(LFS_TRS_DepartmentOfMotorVehicles) OVER() AS LFS_TRS_DepartmentOfMotorVehicles,
    ML.STANDARD_SCALER(LSR_SPO_Stadium) OVER() AS LSR_SPO_Stadium,
    ML.STANDARD_SCALER(LSR_SPO_SnowMountain) OVER() AS LSR_SPO_SnowMountain,
    ML.STANDARD_SCALER(LSR_SPO_Tennis) OVER() AS LSR_SPO_Tennis,
    ML.STANDARD_SCALER(LSR_SPO_Golf) OVER() AS LSR_SPO_Golf,
    ML.STANDARD_SCALER(LSR_SPO_Pool) OVER() AS LSR_SPO_Pool,
    ML.STANDARD_SCALER(probability_age_10s) OVER() AS probability_age_10s,
    ML.STANDARD_SCALER(probability_age_20s) OVER() AS probability_age_20s,
    ML.STANDARD_SCALER(probability_age_30s) OVER() AS probability_age_30s,
    ML.STANDARD_SCALER(probability_age_40s) OVER() AS probability_age_40s,
    ML.STANDARD_SCALER(probability_age_50s) OVER() AS probability_age_50s,
    ML.STANDARD_SCALER(probability_age_60_70s) OVER() AS probability_age_60_70s,
    ML.STANDARD_SCALER(probability_male) OVER() AS probability_male,
    home_pref,
    home_city,
    office_pref,
    office_city,
    upper1_nm,
    upper2_nm,
    quantity,
    ML.STANDARD_SCALER(avg_price) OVER() AS avg_price,
  from
    handle_missing
),

cat_encoding as (
  select
    sales_date,
    adid,
    LSR_SPO_SportsHall,
    LSR_SPO_FitnessGym,
    LSR_SPO_OtherSports,
    LSR_OTD_ParkGarden,
    LSR_OTD_BeachRiver,
    SPG_SPC_BookStore,
    SPG_SPC_RentVideoCD,
    SPG_SPC_HousingExpo,
    SPG_SPC_OutdoorGoodsStore,
    SPG_SPC_LiquorStore,
    LSR_AMS_Arcade,
    SPG_SPC_PreOwnedCarDealer,
    SPG_SPC_BabyKids,
    LSR_AMS_Karaoke,
    SPG_SPC_OtherSpecialityStore,
    LFS_CAR_Massage,
    LFS_CAR_Aesthetic,
    SPG_FSH_Lingerie,
    LFS_CAR_Spa,
    LFS_CAR_PetService,
    SPG_FSH_SecondHand,
    GRM_SPC_Chinese,
    GRM_SPC_Italian,
    GRM_SPC_NigiriSushi,
    GRM_SPC_Steak,
    GRM_SPC_OtherRestaurant,
    SPG_LRG_HomeApplianceStore,
    LFS_PUB_Police,
    SPG_LRG_HomeCenter,
    LFS_PUB_GovernmentOffice,
    LFS_PUB_Courtyard,
    SPG_LRG_FurnitureStore,
    SPG_LRG_OtherSC,
    GRM_CFS_Cafe,
    SPG_SML_MiniMarket,
    LFS_HSP_Clinics,
    SPG_SML_ConvenienceStore,
    SPG_SML_OneDollerMarket,
    GRM_FMR_Japanese,
    GRM_FMR_Western,
    GRM_FMR_Chinese,
    GRM_FMR_Variety,
    LSR_OTR_OtherEntertainment,
    LFS_SCL_LanguageSchool,
    LFS_SCL_ArtCraft,
    LFS_SCL_TraditionalCulture,
    LFS_SCL_Dance,
    LFS_SCL_Music,
    LFS_SCL_Cooking,
    GRM_FST_HamburgerShop,
    GRM_FST_UdonSoba,
    GRM_FST_Takeout,
    LSR_ACM_Hotel,
    LSR_ACM_JapaneseHotel,
    LFS_EDU_HighSchool,
    LSR_ACM_BnB,
    LFS_EDU_ElementarySchool,
    LSR_ACM_LeisureHotel,
    LFS_EDU_VocationalSchool,
    LFS_EDU_PrepSchool,
    LFS_FIN_Bank,
    LSR_APR_ArtMuseum,
    LSR_APR_Archive,
    LSR_APR_Aquarium,
    LSR_APR_LiveHouse,
    LSR_APR_OtherAppreciation,
    LFS_TRS_FerryTerminal,
    LFS_TRS_SAPA,
    LFS_TRS_CarCareService,
    LFS_TRS_Station,
    LFS_TRS_BusTerminal,
    LSR_SPO_Ballpark,
    LSR_SPO_Budo,
    LSR_OTD_ShrineTemple,
    LSR_OTD_Camp,
    SPG_SPC_CarSupplyStore,
    SPG_SPC_SportsGoodsStore,
    SPG_SPC_MobileStore,
    LSR_AMS_Themepark,
    SPG_SPC_CarDealer,
    LSR_AMS_InternetComicCafe,
    SPG_FSH_Apparel,
    LFS_CAR_Hair,
    SPG_FSH_Kimono,
    SPG_FSH_FormalWear,
    SPG_FSH_ShoeStore,
    LFS_CAR_Laundry,
    SPG_FSH_Bag,
    SPG_FSH_Accessory,
    GRM_SPC_AsianEthnic,
    GRM_SPC_French,
    GRM_SPC_Izakaya,
    GRM_SPC_BarPubClub,
    GRM_SPC_TraditionalJapanese,
    GRM_SPC_Unagi,
    GRM_SPC_Yakiniku,
    SPG_LRG_DepartmentStore,
    LFS_PUB_MunicipalOffice,
    LFS_PUB_FireFighting,
    SPG_LRG_ShoppingMallComplex,
    LFS_PUB_FuneralWedding,
    SPG_LRG_OutletMall,
    LFS_PUB_Embassies,
    SPG_LRG_RecycleShop,
    SPG_LRG_MiscellaneousGoodsStore,
    LFS_PUB_Library,
    GRM_CFS_JapaneseSweets,
    GRM_CFS_WesternSweets,
    GRM_CFS_OtherCafeSweets,
    SPG_SML_FoodMarket,
    LFS_HSP_GeneralHospitals,
    SPG_SML_DrugStore,
    SPG_SML_DiscountStore,
    GRM_FMR_Yakiniku,
    GRM_FMR_Sushi,
    LFS_SCL_BusinessSchool,
    LFS_SCL_Computer,
    GRM_FST_DonTeishoku,
    GRM_FST_Ramen,
    GRM_FST_Bakery,
    GRM_FST_Curry,
    GRM_FST_OtherFastFood,
    LFS_EDU_University,
    LSR_ACM_BusinessHotel,
    LFS_EDU_JuniorHighSchool,
    LFS_EDU_KindergartenNursery,
    LSR_ACM_OtherAccomodation,
    LSR_APR_Theater,
    LSR_APR_Hall,
    LFS_FIN_PostOffice,
    LSR_APR_Museum,
    LSR_APR_ZooBotanical,
    LFS_TRS_Parking,
    LFS_TRS_GasStation,
    LFS_TRS_RoadsideRest,
    LFS_TRS_Airport,
    LFS_TRS_CarRental,
    LFS_TRS_DepartmentOfMotorVehicles,
    LSR_SPO_Stadium,
    LSR_SPO_SnowMountain,
    LSR_SPO_Tennis,
    LSR_SPO_Golf,
    LSR_SPO_Pool,
    probability_age_10s,
    probability_age_20s,
    probability_age_30s,
    probability_age_40s,
    probability_age_50s,
    probability_age_60_70s,
    probability_male,
    ML.LABEL_ENCODER(home_pref, 1000000, 1) OVER () AS home_pref,
    ML.LABEL_ENCODER(home_city, 1000000, 1) OVER () AS home_city,
    ML.LABEL_ENCODER(office_pref, 1000000, 1) OVER () AS office_pref,
    ML.LABEL_ENCODER(office_city, 1000000, 1) OVER () AS office_city,
    ML.LABEL_ENCODER(upper1_nm, 1000000, 1) OVER () AS upper1_nm,
    ML.LABEL_ENCODER(upper2_nm, 1000000, 1) OVER () AS upper2_nm,
    quantity,
    avg_price
  from
    standardize
),

-- ユーザー特徴量
users as (
  select distinct
    adid,
    LSR_SPO_SportsHall,
    LSR_SPO_FitnessGym,
    LSR_SPO_OtherSports,
    LSR_OTD_ParkGarden,
    LSR_OTD_BeachRiver,
    SPG_SPC_BookStore,
    SPG_SPC_RentVideoCD,
    SPG_SPC_HousingExpo,
    SPG_SPC_OutdoorGoodsStore,
    SPG_SPC_LiquorStore,
    LSR_AMS_Arcade,
    SPG_SPC_PreOwnedCarDealer,
    SPG_SPC_BabyKids,
    LSR_AMS_Karaoke,
    SPG_SPC_OtherSpecialityStore,
    LFS_CAR_Massage,
    LFS_CAR_Aesthetic,
    SPG_FSH_Lingerie,
    LFS_CAR_Spa,
    LFS_CAR_PetService,
    SPG_FSH_SecondHand,
    GRM_SPC_Chinese,
    GRM_SPC_Italian,
    GRM_SPC_NigiriSushi,
    GRM_SPC_Steak,
    GRM_SPC_OtherRestaurant,
    SPG_LRG_HomeApplianceStore,
    LFS_PUB_Police,
    SPG_LRG_HomeCenter,
    LFS_PUB_GovernmentOffice,
    LFS_PUB_Courtyard,
    SPG_LRG_FurnitureStore,
    SPG_LRG_OtherSC,
    GRM_CFS_Cafe,
    SPG_SML_MiniMarket,
    LFS_HSP_Clinics,
    SPG_SML_ConvenienceStore,
    SPG_SML_OneDollerMarket,
    GRM_FMR_Japanese,
    GRM_FMR_Western,
    GRM_FMR_Chinese,
    GRM_FMR_Variety,
    LSR_OTR_OtherEntertainment,
    LFS_SCL_LanguageSchool,
    LFS_SCL_ArtCraft,
    LFS_SCL_TraditionalCulture,
    LFS_SCL_Dance,
    LFS_SCL_Music,
    LFS_SCL_Cooking,
    GRM_FST_HamburgerShop,
    GRM_FST_UdonSoba,
    GRM_FST_Takeout,
    LSR_ACM_Hotel,
    LSR_ACM_JapaneseHotel,
    LFS_EDU_HighSchool,
    LSR_ACM_BnB,
    LFS_EDU_ElementarySchool,
    LSR_ACM_LeisureHotel,
    LFS_EDU_VocationalSchool,
    LFS_EDU_PrepSchool,
    LFS_FIN_Bank,
    LSR_APR_ArtMuseum,
    LSR_APR_Archive,
    LSR_APR_Aquarium,
    LSR_APR_LiveHouse,
    LSR_APR_OtherAppreciation,
    LFS_TRS_FerryTerminal,
    LFS_TRS_SAPA,
    LFS_TRS_CarCareService,
    LFS_TRS_Station,
    LFS_TRS_BusTerminal,
    LSR_SPO_Ballpark,
    LSR_SPO_Budo,
    LSR_OTD_ShrineTemple,
    LSR_OTD_Camp,
    SPG_SPC_CarSupplyStore,
    SPG_SPC_SportsGoodsStore,
    SPG_SPC_MobileStore,
    LSR_AMS_Themepark,
    SPG_SPC_CarDealer,
    LSR_AMS_InternetComicCafe,
    SPG_FSH_Apparel,
    LFS_CAR_Hair,
    SPG_FSH_Kimono,
    SPG_FSH_FormalWear,
    SPG_FSH_ShoeStore,
    LFS_CAR_Laundry,
    SPG_FSH_Bag,
    SPG_FSH_Accessory,
    GRM_SPC_AsianEthnic,
    GRM_SPC_French,
    GRM_SPC_Izakaya,
    GRM_SPC_BarPubClub,
    GRM_SPC_TraditionalJapanese,
    GRM_SPC_Unagi,
    GRM_SPC_Yakiniku,
    SPG_LRG_DepartmentStore,
    LFS_PUB_MunicipalOffice,
    LFS_PUB_FireFighting,
    SPG_LRG_ShoppingMallComplex,
    LFS_PUB_FuneralWedding,
    SPG_LRG_OutletMall,
    LFS_PUB_Embassies,
    SPG_LRG_RecycleShop,
    SPG_LRG_MiscellaneousGoodsStore,
    LFS_PUB_Library,
    GRM_CFS_JapaneseSweets,
    GRM_CFS_WesternSweets,
    GRM_CFS_OtherCafeSweets,
    SPG_SML_FoodMarket,
    LFS_HSP_GeneralHospitals,
    SPG_SML_DrugStore,
    SPG_SML_DiscountStore,
    GRM_FMR_Yakiniku,
    GRM_FMR_Sushi,
    LFS_SCL_BusinessSchool,
    LFS_SCL_Computer,
    GRM_FST_DonTeishoku,
    GRM_FST_Ramen,
    GRM_FST_Bakery,
    GRM_FST_Curry,
    GRM_FST_OtherFastFood,
    LFS_EDU_University,
    LSR_ACM_BusinessHotel,
    LFS_EDU_JuniorHighSchool,
    LFS_EDU_KindergartenNursery,
    LSR_ACM_OtherAccomodation,
    LSR_APR_Theater,
    LSR_APR_Hall,
    LFS_FIN_PostOffice,
    LSR_APR_Museum,
    LSR_APR_ZooBotanical,
    LFS_TRS_Parking,
    LFS_TRS_GasStation,
    LFS_TRS_RoadsideRest,
    LFS_TRS_Airport,
    LFS_TRS_CarRental,
    LFS_TRS_DepartmentOfMotorVehicles,
    LSR_SPO_Stadium,
    LSR_SPO_SnowMountain,
    LSR_SPO_Tennis,
    LSR_SPO_Golf,
    LSR_SPO_Pool,
    probability_age_10s,
    probability_age_20s,
    probability_age_30s,
    probability_age_40s,
    probability_age_50s,
    probability_age_60_70s,
    probability_male,
    home_pref,
    home_city,
    office_pref,
    office_city
  from
    cat_encoding
),

items as (
  select distinct
    upper1_nm,
    upper2_nm,
    avg_price,
  from
    cat_encoding
),

positives as (
  select
    adid,
    LSR_SPO_SportsHall,
    LSR_SPO_FitnessGym,
    LSR_SPO_OtherSports,
    LSR_OTD_ParkGarden,
    LSR_OTD_BeachRiver,
    SPG_SPC_BookStore,
    SPG_SPC_RentVideoCD,
    SPG_SPC_HousingExpo,
    SPG_SPC_OutdoorGoodsStore,
    SPG_SPC_LiquorStore,
    LSR_AMS_Arcade,
    SPG_SPC_PreOwnedCarDealer,
    SPG_SPC_BabyKids,
    LSR_AMS_Karaoke,
    SPG_SPC_OtherSpecialityStore,
    LFS_CAR_Massage,
    LFS_CAR_Aesthetic,
    SPG_FSH_Lingerie,
    LFS_CAR_Spa,
    LFS_CAR_PetService,
    SPG_FSH_SecondHand,
    GRM_SPC_Chinese,
    GRM_SPC_Italian,
    GRM_SPC_NigiriSushi,
    GRM_SPC_Steak,
    GRM_SPC_OtherRestaurant,
    SPG_LRG_HomeApplianceStore,
    LFS_PUB_Police,
    SPG_LRG_HomeCenter,
    LFS_PUB_GovernmentOffice,
    LFS_PUB_Courtyard,
    SPG_LRG_FurnitureStore,
    SPG_LRG_OtherSC,
    GRM_CFS_Cafe,
    SPG_SML_MiniMarket,
    LFS_HSP_Clinics,
    SPG_SML_ConvenienceStore,
    SPG_SML_OneDollerMarket,
    GRM_FMR_Japanese,
    GRM_FMR_Western,
    GRM_FMR_Chinese,
    GRM_FMR_Variety,
    LSR_OTR_OtherEntertainment,
    LFS_SCL_LanguageSchool,
    LFS_SCL_ArtCraft,
    LFS_SCL_TraditionalCulture,
    LFS_SCL_Dance,
    LFS_SCL_Music,
    LFS_SCL_Cooking,
    GRM_FST_HamburgerShop,
    GRM_FST_UdonSoba,
    GRM_FST_Takeout,
    LSR_ACM_Hotel,
    LSR_ACM_JapaneseHotel,
    LFS_EDU_HighSchool,
    LSR_ACM_BnB,
    LFS_EDU_ElementarySchool,
    LSR_ACM_LeisureHotel,
    LFS_EDU_VocationalSchool,
    LFS_EDU_PrepSchool,
    LFS_FIN_Bank,
    LSR_APR_ArtMuseum,
    LSR_APR_Archive,
    LSR_APR_Aquarium,
    LSR_APR_LiveHouse,
    LSR_APR_OtherAppreciation,
    LFS_TRS_FerryTerminal,
    LFS_TRS_SAPA,
    LFS_TRS_CarCareService,
    LFS_TRS_Station,
    LFS_TRS_BusTerminal,
    LSR_SPO_Ballpark,
    LSR_SPO_Budo,
    LSR_OTD_ShrineTemple,
    LSR_OTD_Camp,
    SPG_SPC_CarSupplyStore,
    SPG_SPC_SportsGoodsStore,
    SPG_SPC_MobileStore,
    LSR_AMS_Themepark,
    SPG_SPC_CarDealer,
    LSR_AMS_InternetComicCafe,
    SPG_FSH_Apparel,
    LFS_CAR_Hair,
    SPG_FSH_Kimono,
    SPG_FSH_FormalWear,
    SPG_FSH_ShoeStore,
    LFS_CAR_Laundry,
    SPG_FSH_Bag,
    SPG_FSH_Accessory,
    GRM_SPC_AsianEthnic,
    GRM_SPC_French,
    GRM_SPC_Izakaya,
    GRM_SPC_BarPubClub,
    GRM_SPC_TraditionalJapanese,
    GRM_SPC_Unagi,
    GRM_SPC_Yakiniku,
    SPG_LRG_DepartmentStore,
    LFS_PUB_MunicipalOffice,
    LFS_PUB_FireFighting,
    SPG_LRG_ShoppingMallComplex,
    LFS_PUB_FuneralWedding,
    SPG_LRG_OutletMall,
    LFS_PUB_Embassies,
    SPG_LRG_RecycleShop,
    SPG_LRG_MiscellaneousGoodsStore,
    LFS_PUB_Library,
    GRM_CFS_JapaneseSweets,
    GRM_CFS_WesternSweets,
    GRM_CFS_OtherCafeSweets,
    SPG_SML_FoodMarket,
    LFS_HSP_GeneralHospitals,
    SPG_SML_DrugStore,
    SPG_SML_DiscountStore,
    GRM_FMR_Yakiniku,
    GRM_FMR_Sushi,
    LFS_SCL_BusinessSchool,
    LFS_SCL_Computer,
    GRM_FST_DonTeishoku,
    GRM_FST_Ramen,
    GRM_FST_Bakery,
    GRM_FST_Curry,
    GRM_FST_OtherFastFood,
    LFS_EDU_University,
    LSR_ACM_BusinessHotel,
    LFS_EDU_JuniorHighSchool,
    LFS_EDU_KindergartenNursery,
    LSR_ACM_OtherAccomodation,
    LSR_APR_Theater,
    LSR_APR_Hall,
    LFS_FIN_PostOffice,
    LSR_APR_Museum,
    LSR_APR_ZooBotanical,
    LFS_TRS_Parking,
    LFS_TRS_GasStation,
    LFS_TRS_RoadsideRest,
    LFS_TRS_Airport,
    LFS_TRS_CarRental,
    LFS_TRS_DepartmentOfMotorVehicles,
    LSR_SPO_Stadium,
    LSR_SPO_SnowMountain,
    LSR_SPO_Tennis,
    LSR_SPO_Golf,
    LSR_SPO_Pool,
    probability_age_10s,
    probability_age_20s,
    probability_age_30s,
    probability_age_40s,
    probability_age_50s,
    probability_age_60_70s,
    probability_male,
    home_pref,
    home_city,
    office_pref,
    office_city,
    upper1_nm,
    upper2_nm,
    avg_price,
    sum(quantity) as sum_quantity,
    1 as label,
  from
    cat_encoding
  group by all
),

negative_candidates as (
  select
    u.*,
    i.upper1_nm,
    i.upper2_nm,
    i.avg_price,
  from users u
  cross join items i
  left join positives p on p.adid = u.adid and p.upper1_nm = i.upper1_nm and p.upper2_nm = i.upper2_nm
  where p.upper2_nm is null
),

sampled_negatives as (
  select
    *,
    1 as sum_quantity,
    0 as label,
    row_number() over (partition by adid order by rand()) as rn
  from
    negative_candidates
),

final as (
  select
    *
  from
    positives

  union all

  select
    * except(rn)
  from
    sampled_negatives
  where rn <= 5
)

select *
from final
order by adid




