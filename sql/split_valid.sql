DECLARE train_ratio FLOAT64;
DECLARE valid_ratio FLOAT64;
DECLARE test_ratio FLOAT64;

set train_ratio = 0.6;
set valid_ratio = 0.2;
set test_ratio = 0.2;
set @@query_label = 'cost:5867';

-- train data
create or replace table `labs-science.works_ueno.two_tower_model_sugi_v3_valid_data` as

with pos_data as (
  select
    *,
    row_number() over (order by sales_date) as row_num,
    count(*) over () as total_count
  from
    `labs-science.works_ueno.sugi_v3_user_item_data_filtered_adid_in_bb`
)

select
  * except(row_num, total_count)
from pos_data
where row_num between cast(floor(total_count * train_ratio) as int64) and cast(floor(total_count * (train_ratio + valid_ratio)) as int64)
order by sales_date asc

